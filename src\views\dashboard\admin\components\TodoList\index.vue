<template>
  <section class="todoapp">
    <!-- header -->
    <header class="header">
      <input class="new-todo" autocomplete="off" placeholder="添加待办事项" @keyup.enter="addTodo">
    </header>
    <!-- main section -->
    <section v-show="todos.length" class="main">
      <input id="toggle-all" :checked="allChecked" class="toggle-all" type="checkbox" @change="toggleAll({ done: !allChecked })">
      <label for="toggle-all" />
      <ul class="todo-list">
        <todo
          v-for="(todo, index) in filteredTodos"
          :key="index"
          :todo="todo"
          @toggleTodo="toggleTodo"
          @editTodo="editTodo"
          @deleteTodo="deleteTodo"
        />
      </ul>
    </section>
    <!-- footer -->
    <footer v-show="todos.length" class="footer">
      <span class="todo-count">
        <strong>{{ remaining }}</strong>
        {{ remaining | pluralize('项') }} 待处理
      </span>
      <ul class="filters">
        <li v-for="(val, key) in filters" :key="key">
          <a :class="{ selected: visibility === key }" @click.prevent="visibility = key">{{ key | capitalize }}</a>
        </li>
      </ul>
      <!-- <button class="clear-completed" v-show="todos.length > remaining" @click="clearCompleted">
        Clear completed
      </button> -->
    </footer>
  </section>
</template>

<script>
import Todo from './Todo.vue'
import { getTodoList } from '@/api/dashboard'

const STORAGE_KEY = 'rental-todos'
const filters = {
  all: todos => todos,
  active: todos => todos.filter(todo => !todo.done),
  completed: todos => todos.filter(todo => todo.done)
}

export default {
  components: { Todo },
  filters: {
    pluralize: (n, w) => n === 1 ? w : w,
    capitalize: s => s.charAt(0).toUpperCase() + s.slice(1)
  },
  data() {
    return {
      visibility: 'all',
      filters,
      todos: [],
      loading: false
    }
  },
  computed: {
    allChecked() {
      return this.todos.every(todo => todo.done)
    },
    filteredTodos() {
      return filters[this.visibility](this.todos)
    },
    remaining() {
      return this.todos.filter(todo => !todo.done).length
    }
  },
  mounted() {
    this.loadTodos()
  },
  methods: {
    async loadTodos() {
      this.loading = true
      try {
        const response = await getTodoList()
        this.todos = response || []
        
        // 加载本地存储的自定义待办事项
        const localTodos = JSON.parse(window.localStorage.getItem(STORAGE_KEY) || '[]')
        const customTodos = localTodos.filter(todo => todo.isCustom)
        
        // 合并API返回的待办事项和本地自定义待办事项
        this.todos = [...this.todos, ...customTodos]
        
      } catch (error) {
        console.error('获取待办事项失败:', error)
        this.$message.error('获取待办事项失败')
        // 使用默认待办事项
        this.todos = [
          { text: '检查未发货订单', done: false, isCustom: false },
          { text: '联系逾期客户', done: false, isCustom: false },
          { text: '更新设备状态', done: false, isCustom: false }
        ]
      } finally {
        this.loading = false
      }
    },
    setLocalStorage() {
      // 只保存自定义的待办事项到本地存储
      const customTodos = this.todos.filter(todo => todo.isCustom)
      window.localStorage.setItem(STORAGE_KEY, JSON.stringify(customTodos))
    },
    addTodo(e) {
      const text = e.target.value
      if (text.trim()) {
        this.todos.unshift({
          text,
          done: false,
          priority: 'medium',
          isCustom: true  // 标记为自定义待办事项
        })
        this.setLocalStorage()
      }
      e.target.value = ''
    },
    toggleTodo(val) {
      val.done = !val.done
      if (val.isCustom) {
        this.setLocalStorage()
      }
    },
    deleteTodo(todo) {
      this.todos.splice(this.todos.indexOf(todo), 1)
      if (todo.isCustom) {
        this.setLocalStorage()
      }
    },
    editTodo({ todo, value }) {
      todo.text = value
      if (todo.isCustom) {
        this.setLocalStorage()
      }
    },
    clearCompleted() {
      this.todos = this.todos.filter(todo => !todo.done)
      this.setLocalStorage()
    },
    toggleAll({ done }) {
      this.todos.forEach(todo => {
        todo.done = done
      })
      this.setLocalStorage()
    }
  }
}
</script>

<style lang="scss">
  @import './index.scss';
</style>
