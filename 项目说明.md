# 租赁管理系统

一个基于Vue Admin + FastAPI的设备租赁管理系统，用于管理设备和租赁记录。

## 项目结构

```
dazuizulin/
├── backend/                    # FastAPI后端
│   ├── app/                    # 应用核心
│   │   ├── auth.py            # 认证相关
│   │   └── schemas.py         # 数据模式
│   ├── database/               # 数据库配置
│   ├── models/                 # 数据库模型
│   ├── routers/                # API路由
│   ├── config.py              # 配置文件
│   ├── main.py                # 主应用
│   ├── requirements.txt       # Python依赖
│   └── start.py               # 启动脚本
├── src/                       # Vue前端源码
│   ├── api/                   # API接口
│   ├── views/                 # 页面组件
│   │   ├── machine/           # 机器管理页面
│   │   └── rental/            # 租赁管理页面
│   └── ...                    # 其他前端文件
├── 需求.txt                   # 项目需求文档
├── 开发计划.txt               # 开发计划
└── README.md
```

## 功能特性

### 用户管理
- 用户注册和登录
- JWT Token认证
- 密码加密存储

### 机器管理
- 添加/编辑/删除机器信息
- 设备编号、SN码管理
- 购买时间和随心换到期日期跟踪

### 租赁管理
- 创建/编辑/删除租赁记录
- 租赁状态跟踪（未发货、已发货、已收货、已归还）
- 快递单号管理
- 客户信息和地址管理
- 配件备注和租金管理

## 技术栈

### 后端
- **FastAPI**: 现代Python Web框架
- **SQLAlchemy**: ORM数据库操作
- **SQLite**: 轻量级数据库
- **Pydantic**: 数据验证
- **JWT**: 用户认证
- **Uvicorn**: ASGI服务器

### 前端
- **Vue 2**: 渐进式JavaScript框架
- **Element UI**: Vue UI组件库
- **Vue Router**: 路由管理
- **Vuex**: 状态管理
- **Axios**: HTTP客户端

## 安装和运行

### 环境要求
- Python 3.7+
- Node.js 14+
- npm 或 yarn

### 后端启动

1. 进入backend目录：
```bash
cd backend
```

2. 安装Python依赖：
```bash
pip install -r requirements.txt
```

3. 启动后端服务：
```bash
python start.py
```

后端服务将运行在 http://localhost:8000

### 前端启动

1. 安装前端依赖：
```bash
npm install
```

2. 启动开发服务器：
```bash
npm run dev
```

前端服务将运行在 http://localhost:9527

## 使用说明

### 首次使用

1. 启动后端和前端服务
2. 在浏览器中访问 http://localhost:9527
3. 使用默认管理员账号登录：
   - **用户名**: admin
   - **密码**: Admin@2024#Secure!
4. 登录后即可使用系统功能

⚠️ **安全提示**: 建议首次登录后立即修改默认密码！

### 主要功能

#### 机器管理
- 访问"机器管理" -> "机器列表"
- 点击"添加机器"按钮添加新设备
- 填写设备编号、SN码、购买时间和随心换到期日期
- 可以编辑或删除现有机器信息

#### 租赁管理
- 访问"租赁管理" -> "租赁列表"  
- 点击"添加租赁记录"按钮创建新租赁
- 填写必填字段：开始时间、结束时间、租金、配件备注
- 可选填写：设备编号、闲鱼单号、快递单号、客户信息等
- 可以查看详情、编辑或删除租赁记录

## API文档

后端启动后，可以访问以下地址查看API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 开发计划

项目按照以下阶段开发：

### ✅ 第一阶段：后端API开发
- [x] 环境搭建和项目初始化
- [x] 数据库设计和模型创建  
- [x] 用户认证系统
- [x] 机器管理API
- [x] 租赁管理API

### ✅ 第二阶段：前端页面开发
- [x] 登录认证模块适配
- [x] 机器管理页面
- [x] 租赁管理页面
- [x] 路由配置

### 第三阶段：功能完善和优化
- [ ] 数据验证优化
- [ ] 界面美化
- [ ] 性能优化
- [ ] 功能测试

## 注意事项

1. 确保后端服务先启动，前端才能正常工作
2. 首次运行会自动创建SQLite数据库
3. 开发环境下前端会代理API请求到后端8000端口
4. 默认给所有用户管理员权限，生产环境需要完善权限控制

## 故障排除

### 常见问题

1. **前端无法连接后端**
   - 确认后端服务正在运行（http://localhost:8000）
   - 检查vue.config.js中的代理配置

2. **数据库错误**
   - 删除backend目录下的rental_system.db文件重新创建
   - 检查Python依赖是否正确安装

3. **前端依赖安装失败**
   - 尝试删除node_modules文件夹重新安装
   - 使用npm或yarn替代安装

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

本项目采用MIT许可证。 