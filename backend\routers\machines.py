from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from database.database import get_db
from models.machine import Machine
from models.user import User
from app.schemas import MachineCreate, Machine as MachineSchema, MachineUpdate
from app.auth import get_current_user

router = APIRouter(prefix="/machines", tags=["机器管理"])

@router.post("/", response_model=MachineSchema)
def create_machine(
    machine: MachineCreate, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 检查当前用户的设备编号是否已存在
    db_machine = db.query(Machine).filter(
        Machine.device_number == machine.device_number,
        Machine.user_id == current_user.id
    ).first()
    if db_machine:
        raise HTTPException(status_code=400, detail="设备编号已存在")
    
    # 检查当前用户的SN码是否已存在
    db_machine = db.query(Machine).filter(
        Machine.sn_code == machine.sn_code,
        Machine.user_id == current_user.id
    ).first()
    if db_machine:
        raise HTTPException(status_code=400, detail="SN码已存在")
    
    # 创建机器时自动设置user_id
    machine_data = machine.dict()
    machine_data["user_id"] = current_user.id
    db_machine = Machine(**machine_data)
    db.add(db_machine)
    db.commit()
    db.refresh(db_machine)
    return db_machine

@router.get("/")
def read_machines(
    skip: int = 0, 
    limit: int = 50, 
    sort: str = None,
    search: str = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 创建基本查询
    query = db.query(Machine).filter(Machine.user_id == current_user.id)
    
    # 添加搜索功能
    if search:
        query = query.filter(
            Machine.device_number.ilike(f"%{search}%") | 
            Machine.sn_code.ilike(f"%{search}%")
        )
    
    # 获取总记录数
    total = query.count()
    
    # 添加排序功能
    if sort:
        # 检查字段是否存在于Machine模型中
        valid_sort_fields = [column.name for column in Machine.__table__.columns]
        
        # 处理降序排序（以"-"开头）
        if sort.startswith('-'):
            sort_field = sort[1:]
            if sort_field in valid_sort_fields:
                query = query.order_by(getattr(Machine, sort_field).desc())
        else:
            # 升序排序
            if sort in valid_sort_fields:
                query = query.order_by(getattr(Machine, sort).asc())
    else:
        # 默认按照ID倒序排序
        query = query.order_by(Machine.id.desc())
    
    # 分页
    machines = query.offset(skip).limit(limit).all()
    
    # 转换为字典列表
    machine_list = []
    for machine in machines:
        machine_dict = {
            "id": machine.id,
            "device_number": machine.device_number,
            "sn_code": machine.sn_code,
            "device_type": machine.device_type,
            "purchase_date": str(machine.purchase_date),
            "warranty_expire_date": str(machine.warranty_expire_date),
            "user_id": machine.user_id,
            "created_at": machine.created_at.isoformat() if machine.created_at else None
        }
        machine_list.append(machine_dict)
    
    # 返回带有总数的响应
    return {
        "items": machine_list,
        "total": total
    }

@router.get("/{machine_id}", response_model=MachineSchema)
def read_machine(
    machine_id: int, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 只返回当前用户的机器
    machine = db.query(Machine).filter(
        Machine.id == machine_id,
        Machine.user_id == current_user.id
    ).first()
    if machine is None:
        raise HTTPException(status_code=404, detail="机器不存在")
    return machine

@router.put("/{machine_id}", response_model=MachineSchema)
def update_machine(
    machine_id: int, 
    machine_update: MachineUpdate, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 只更新当前用户的机器
    machine = db.query(Machine).filter(
        Machine.id == machine_id,
        Machine.user_id == current_user.id
    ).first()
    if machine is None:
        raise HTTPException(status_code=404, detail="机器不存在")
    
    update_data = machine_update.dict(exclude_unset=True)
    
    # 如果更新设备编号，检查是否与当前用户的其他机器冲突
    if "device_number" in update_data:
        existing_machine = db.query(Machine).filter(
            Machine.device_number == update_data["device_number"],
            Machine.user_id == current_user.id,
            Machine.id != machine_id
        ).first()
        if existing_machine:
            raise HTTPException(status_code=400, detail="设备编号已存在")
    
    # 如果更新SN码，检查是否与当前用户的其他机器冲突
    if "sn_code" in update_data:
        existing_machine = db.query(Machine).filter(
            Machine.sn_code == update_data["sn_code"],
            Machine.user_id == current_user.id,
            Machine.id != machine_id
        ).first()
        if existing_machine:
            raise HTTPException(status_code=400, detail="SN码已存在")
    
    for field, value in update_data.items():
        setattr(machine, field, value)
    
    db.commit()
    db.refresh(machine)
    return machine

@router.delete("/{machine_id}")
def delete_machine(
    machine_id: int, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 只删除当前用户的机器
    machine = db.query(Machine).filter(
        Machine.id == machine_id,
        Machine.user_id == current_user.id
    ).first()
    if machine is None:
        raise HTTPException(status_code=404, detail="机器不存在")
    
    db.delete(machine)
    db.commit()
    return {"message": "机器删除成功"} 