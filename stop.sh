#!/bin/bash

# 停止租赁管理系统服务
echo "🛑 停止租赁管理系统服务..."

# 停止后端服务
echo "🔧 查找并停止后端服务..."
BACKEND_PID=$(ps aux | grep "python start.py" | grep -v grep | awk '{print $2}')

if [ ! -z "$BACKEND_PID" ]; then
    echo "找到后端进程 PID: $BACKEND_PID"
    kill $BACKEND_PID
    echo "✅ 后端服务已停止"
else
    echo "⚠️  未找到运行中的后端服务"
fi

# 停止nginx（可选）
read -p "是否停止nginx服务？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🌐 停止nginx服务..."
    sudo systemctl stop nginx
    echo "✅ nginx服务已停止"
fi

echo "🎉 所有服务已停止" 