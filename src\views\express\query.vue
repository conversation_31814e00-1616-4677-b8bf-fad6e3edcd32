<template>
  <div class="logistics-query-container">
    <div class="app-container">
      <div class="logistics-query-header">
        <h3>物流查询</h3>
        <p>查询快递物流信息</p>
      </div>
      <div class="iframe-container">
        <iframe
          src="https://www.sf-express.com/chn/sc/waybill/waybill-detail/SF1450942404761"
          frameborder="0"
          class="logistics-iframe"
          title="物流查询"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LogisticsQuery',
  data() {
    return {}
  },
  mounted() {
    console.log('物流查询页面已加载')
  }
}
</script>

<style lang="scss" scoped>
.logistics-query-container {
  padding: 0;
  height: 100vh;
  
  .app-container {
    height: 100%;
    padding: 20px;
    
    .logistics-query-header {
      margin-bottom: 20px;
      
      h3 {
        margin: 0 0 8px 0;
        color: #303133;
        font-size: 18px;
        font-weight: 500;
      }
      
      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
      }
    }
    
    .iframe-container {
      height: calc(100vh - 140px);
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      overflow: hidden;
      
      .logistics-iframe {
        width: 100%;
        height: 100%;
        border: none;
      }
    }
  }
}
</style> 