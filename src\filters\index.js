// import parseTime, formatTime and set to filter
export { parseTime, formatTime, maskSnCode } from '@/utils'

/**
 * Show plural label if time is plural number
 * @param {number} time
 * @param {string} label
 * @return {string}
 */
function pluralize(time, label) {
  if (time === 1) {
    return time + label
  }
  return time + label + 's'
}

/**
 * @param {number} time
 */
export function timeAgo(time) {
  const between = Date.now() / 1000 - Number(time)
  if (between < 3600) {
    return pluralize(~~(between / 60), ' minute')
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), ' hour')
  } else {
    return pluralize(~~(between / 86400), ' day')
  }
}

/**
 * Number formatting
 * like 10000 => 10k
 * @param {number} num
 * @param {number} digits
 */
export function numberFormatter(num, digits) {
  const si = [
    { value: 1E18, symbol: 'E' },
    { value: 1E15, symbol: 'P' },
    { value: 1E12, symbol: 'T' },
    { value: 1E9, symbol: 'G' },
    { value: 1E6, symbol: 'M' },
    { value: 1E3, symbol: 'k' }
  ]
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (num / si[i].value).toFixed(digits).replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol
    }
  }
  return num.toString()
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter(num) {
  return (+num || 0).toString().replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

/**
 * Upper case first char
 * @param {String} string
 */
export function uppercaseFirst(string) {
  return string.charAt(0).toUpperCase() + string.slice(1)
}

/**
 * 对SN码进行脱敏处理过滤器
 * @param {string} snCode SN码
 * @returns {string} 脱敏后的SN码
 */
export function snCodeMask(snCode) {
  if (!snCode || typeof snCode !== 'string') {
    return snCode || ''
  }
  
  const length = snCode.length
  
  // 如果长度小于等于8位，则不脱敏
  if (length <= 8) {
    return snCode
  }
  
  // 计算前后保留的位数
  const prefixLength = Math.ceil((length - 8) / 2)
  const suffixLength = length - 8 - prefixLength
  
  // 构造脱敏字符串：前缀 + 8个* + 后缀
  const prefix = snCode.substring(0, prefixLength)
  const suffix = snCode.substring(length - suffixLength)
  const masked = '********'
  
  return prefix + masked + suffix
}
