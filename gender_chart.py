#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性别分析可视化图表
生成性别分布的饼图和柱状图
"""

import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def create_gender_charts():
    """创建性别分析图表"""
    
    # 数据
    categories = ['男性', '女性', '无法判断']
    counts = [1841, 1075, 1441]
    percentages = [42.3, 24.7, 33.1]
    colors = ['#4CAF50', '#FF9800', '#9E9E9E']
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 饼图
    wedges, texts, autotexts = ax1.pie(counts, labels=categories, colors=colors, 
                                       autopct='%1.1f%%', startangle=90)
    ax1.set_title('姓名性别分布饼图\n(总计: 4357 个姓名)', fontsize=14, fontweight='bold')
    
    # 美化饼图文字
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontsize(12)
        autotext.set_fontweight('bold')
    
    # 柱状图
    bars = ax2.bar(categories, counts, color=colors, alpha=0.8)
    ax2.set_title('姓名性别分布柱状图', fontsize=14, fontweight='bold')
    ax2.set_ylabel('人数', fontsize=12)
    ax2.set_xlabel('性别类别', fontsize=12)
    
    # 在柱状图上添加数值标签
    for i, (bar, count, pct) in enumerate(zip(bars, counts, percentages)):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 20,
                f'{count}\n({pct}%)', ha='center', va='bottom', 
                fontsize=11, fontweight='bold')
    
    # 设置y轴范围
    ax2.set_ylim(0, max(counts) * 1.15)
    
    # 添加网格
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('gender_distribution_chart.png', dpi=300, bbox_inches='tight')
    print("性别分布图表已保存为 gender_distribution_chart.png")
    
    # 显示图表
    plt.show()

def create_ratio_chart():
    """创建男女比例对比图"""
    
    fig, ax = plt.subplots(figsize=(8, 6))
    
    # 数据（只包含男性和女性，排除无法判断的）
    male_count = 1841
    female_count = 1075
    total_gendered = male_count + female_count
    
    male_pct = (male_count / total_gendered) * 100
    female_pct = (female_count / total_gendered) * 100
    
    categories = ['男性', '女性']
    counts = [male_count, female_count]
    percentages = [male_pct, female_pct]
    colors = ['#4CAF50', '#FF9800']
    
    # 创建柱状图
    bars = ax.bar(categories, counts, color=colors, alpha=0.8, width=0.6)
    
    # 设置标题和标签
    ax.set_title(f'男女比例对比\n(排除无法判断的姓名，总计: {total_gendered} 个)', 
                fontsize=14, fontweight='bold')
    ax.set_ylabel('人数', fontsize=12)
    ax.set_xlabel('性别', fontsize=12)
    
    # 在柱状图上添加数值标签
    for i, (bar, count, pct) in enumerate(zip(bars, counts, percentages)):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 20,
                f'{count}\n({pct:.1f}%)', ha='center', va='bottom', 
                fontsize=12, fontweight='bold')
    
    # 添加比例说明
    ratio = male_count / female_count
    ax.text(0.5, max(counts) * 0.8, f'男女比例: {ratio:.2f}:1', 
            ha='center', va='center', fontsize=14, fontweight='bold',
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))
    
    # 设置y轴范围
    ax.set_ylim(0, max(counts) * 1.15)
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='y')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('gender_ratio_chart.png', dpi=300, bbox_inches='tight')
    print("男女比例图表已保存为 gender_ratio_chart.png")
    
    # 显示图表
    plt.show()

def print_summary():
    """打印分析摘要"""
    print("\n" + "="*50)
    print("姓名性别分析摘要")
    print("="*50)
    print(f"总姓名数量: 4,357 个")
    print(f"推测为男性: 1,841 人 (42.3%)")
    print(f"推测为女性: 1,075 人 (24.7%)")
    print(f"无法判断: 1,441 人 (33.1%)")
    print(f"男女比例: 1.71:1")
    print("\n分析说明:")
    print("1. 此分析基于姓名中常见的性别特征字符进行推测")
    print("2. 约1/3的姓名无法明确判断性别")
    print("3. 在可判断性别的姓名中，男性比例明显高于女性")
    print("4. 这可能反映了传统命名习惯或样本的特定来源")
    print("="*50)

def main():
    """主函数"""
    try:
        print("正在生成性别分析图表...")
        
        # 创建图表
        create_gender_charts()
        create_ratio_chart()
        
        # 打印摘要
        print_summary()
        
    except ImportError:
        print("错误：需要安装matplotlib库")
        print("请运行：pip install matplotlib")
    except Exception as e:
        print(f"生成图表时发生错误：{e}")

if __name__ == "__main__":
    main() 