import request from '@/utils/request'

export function getRentals(params) {
  return request({
    url: '/rentals/',
    method: 'get',
    params: params
  })
}

export function getRental(id) {
  return request({
    url: `/rentals/${id}`,
    method: 'get'
  })
}

export function createRental(data) {
  return request({
    url: '/rentals/',
    method: 'post',
    data: data
  })
}

export function updateRental(id, data) {
  return request({
    url: `/rentals/${id}`,
    method: 'put',
    data: data
  })
}

export function deleteRental(id) {
  return request({
    url: `/rentals/${id}`,
    method: 'delete'
  })
}

export function getRentalsByDevice(deviceNumber) {
  return request({
    url: `/rentals/by-device/${deviceNumber}`,
    method: 'get'
  })
}

// 获取排单数据 - 所有设备在指定月份的租赁情况
export function getScheduleData(year, month) {
  return request({
    url: `/rentals/schedule/${year}/${month}`,
    method: 'get'
  })
}

// 自动设备分配相关API
export function getAutoDeviceAllocationPreview(config = {}) {
  return request({
    url: '/rentals/auto-device-allocation/preview',
    method: 'post',
    data: config
  })
}

export function confirmAutoDeviceAllocation(allocationData) {
  return request({
    url: '/rentals/auto-device-allocation/confirm',
    method: 'post',
    data: allocationData
  })
}

// 保留原有自动排单API以兼容性
export function getAutoSchedulePreview(config = {}) {
  return request({
    url: '/rentals/auto-schedule/preview',
    method: 'post',
    data: config
  })
}

export function confirmAutoSchedule(scheduleData) {
  return request({
    url: '/rentals/auto-schedule/confirm',
    method: 'post',
    data: scheduleData
  })
} 

// 获取最近的租赁单列表（用于设备入库）
export function getRecentRentals(params) {
  return request({
    url: '/rentals/recent',
    method: 'get',
    params: params
  })
}

// 获取待发货的租赁单列表（用于设备出库）
export function getPendingRentals(params) {
  return request({
    url: '/rentals/pending',
    method: 'get',
    params: params
  })
}

// 设备入库（更新租赁单状态为已归还）
export function returnDevice(rentalId, snCode) {
  return request({
    url: `/rentals/${rentalId}/return`,
    method: 'post',
    data: { sn_code: snCode }
  })
}

// 设备出库（更新租赁单状态为已发货）
export function shipDevice(rentalId, snCode, trackingNumber) {
  return request({
    url: `/rentals/${rentalId}/ship`,
    method: 'post',
    data: { 
      sn_code: snCode,
      tracking_number: trackingNumber
    }
  })
}