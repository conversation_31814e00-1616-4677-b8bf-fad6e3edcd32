#!/usr/bin/env python3
"""
用户创建脚本
用于创建新的测试用户
"""

import sys
from sqlalchemy.orm import Session
from database.database import SessionLocal
from models.user import User
from app.auth import get_password_hash
from database.init_data import create_sample_data

def create_user(username: str, password: str):
    """创建新用户"""
    db = SessionLocal()
    try:
        # 检查用户是否已存在
        existing_user = db.query(User).filter(User.username == username).first()
        if existing_user:
            print(f"❌ 用户 {username} 已存在!")
            return False
        
        # 创建新用户
        hashed_password = get_password_hash(password)
        new_user = User(
            username=username,
            password=hashed_password
        )
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        print(f"✅ 用户 {username} 创建成功!")
        
        # 为用户创建示例数据
        create_sample_data(db, new_user)
        
        return True
    except Exception as e:
        db.rollback()
        print(f"❌ 创建用户失败: {e}")
        return False
    finally:
        db.close()

def main():
    if len(sys.argv) != 3:
        print("使用方法: python create_user.py <用户名> <密码>")
        print("示例: python create_user.py test123 123456")
        return
    
    username = sys.argv[1]
    password = sys.argv[2]
    
    print(f"🔧 创建用户: {username}")
    success = create_user(username, password)
    
    if success:
        print(f"🎉 用户创建完成!")
        print(f"   用户名: {username}")
        print(f"   密码: {password}")
        print(f"   已为该用户创建示例数据")
    else:
        print("💥 用户创建失败")

if __name__ == "__main__":
    main() 