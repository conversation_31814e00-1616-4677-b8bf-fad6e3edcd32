#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
姓名性别分析程序
分析提取的姓名中男性和女性的比例
"""

import csv
import re
from collections import Counter

def load_names_from_csv(csv_file):
    """
    从CSV文件中加载姓名
    
    Args:
        csv_file (str): CSV文件路径
        
    Returns:
        list: 姓名列表
    """
    names = []
    try:
        with open(csv_file, 'r', encoding='utf-8-sig') as file:
            reader = csv.DictReader(file)
            for row in reader:
                names.append(row['姓名'])
    except Exception as e:
        print(f"读取CSV文件时发生错误：{e}")
    
    return names

def analyze_gender_by_name(name):
    """
    根据姓名分析性别
    
    Args:
        name (str): 姓名
        
    Returns:
        str: 'male', 'female', 'unknown'
    """
    # 常见的女性名字用字
    female_chars = {
        '娜', '丽', '美', '雅', '静', '芳', '艳', '秀', '英', '华', '玉', '兰', '梅', '红', '霞',
        '婷', '莉', '洁', '琳', '萍', '燕', '彩', '春', '菊', '兰', '凤', '洁', '梅', '莹', '茹',
        '惠', '珠', '翠', '雅', '芝', '玲', '凤', '素', '云', '莲', '真', '环', '雪', '荣', '爱',
        '妹', '霞', '香', '月', '莺', '媛', '艳', '瑞', '凡', '佳', '嘉', '琼', '勤', '珍', '贞',
        '莉', '桂', '娣', '叶', '璧', '璐', '娅', '琦', '晶', '妍', '茜', '秋', '珊', '莎', '锦',
        '黛', '青', '倩', '婷', '姣', '婉', '娴', '瑾', '颖', '露', '瑶', '怡', '婵', '雁', '蓓',
        '纨', '仪', '荷', '丹', '蓉', '眉', '君', '琴', '蕊', '薇', '菁', '梦', '岚', '苑', '婕',
        '馨', '瑗', '琰', '韵', '融', '园', '艺', '咏', '卿', '聪', '澜', '纯', '毓', '悦', '昭',
        '冰', '爽', '琬', '茗', '羽', '希', '宁', '欣', '飘', '育', '滢', '馥', '筠', '柔', '竹',
        '霭', '凝', '晓', '欢', '霄', '枫', '芸', '菲', '寒', '伊', '亚', '宜', '可', '姬', '舒',
        '影', '荔', '枝', '思', '丽', '秀', '娟', '英', '华', '慧', '巧', '美', '娜', '静', '淑',
        '惠', '珠', '翠', '雅', '芝', '玲', '凤', '素', '云', '莲', '真', '环', '雪', '荣', '爱',
        '妹', '霞', '香', '月', '莺', '媛', '艳', '瑞', '凡', '佳', '嘉', '琼', '勤', '珍', '贞',
        '莉', '桂', '娣', '叶', '璧', '璐', '娅', '琦', '晶', '妍', '茜', '秋', '珊', '莎', '锦',
        '黛', '青', '倩', '婷', '姣', '婉', '娴', '瑾', '颖', '露', '瑶', '怡', '婵', '雁', '蓓'
    }
    
    # 常见的男性名字用字
    male_chars = {
        '伟', '强', '军', '勇', '磊', '超', '明', '杰', '涛', '斌', '辉', '鹏', '华', '建', '国',
        '峰', '龙', '飞', '刚', '平', '东', '文', '武', '志', '清', '山', '波', '浩', '亮', '庆',
        '海', '力', '生', '福', '兴', '友', '才', '发', '成', '康', '星', '光', '天', '达', '安',
        '岩', '中', '茂', '进', '林', '有', '坚', '和', '彪', '博', '诚', '先', '敬', '震', '振',
        '壮', '会', '思', '群', '豪', '心', '邦', '承', '乐', '绍', '功', '松', '善', '厚', '庆',
        '磊', '民', '友', '裕', '河', '哲', '江', '超', '浩', '亮', '政', '谦', '亨', '奇', '固',
        '之', '轮', '翰', '朗', '伯', '宏', '言', '若', '鸣', '朋', '斌', '梁', '栋', '维', '启',
        '克', '伦', '翔', '旭', '鹏', '泽', '晨', '辰', '士', '以', '建', '家', '致', '树', '炎',
        '德', '行', '时', '泰', '盛', '雄', '琛', '钧', '冠', '策', '腾', '楠', '榕', '风', '航',
        '弘', '峻', '晋', '逸', '沛', '镇', '淇', '富', '顺', '信', '子', '杰', '涛', '昌', '成',
        '康', '星', '光', '天', '达', '安', '岩', '中', '茂', '进', '林', '有', '坚', '和', '彪',
        '博', '诚', '先', '敬', '震', '振', '壮', '会', '思', '群', '豪', '心', '邦', '承', '乐',
        '绍', '功', '松', '善', '厚', '庆', '磊', '民', '友', '裕', '河', '哲', '江', '超', '浩',
        '亮', '政', '谦', '亨', '奇', '固', '之', '轮', '翰', '朗', '伯', '宏', '言', '若', '鸣',
        '朋', '斌', '梁', '栋', '维', '启', '克', '伦', '翔', '旭', '鹏', '泽', '晨', '辰', '士'
    }
    
    # 计算男性和女性特征字符的数量
    male_score = 0
    female_score = 0
    
    for char in name:
        if char in male_chars:
            male_score += 1
        if char in female_chars:
            female_score += 1
    
    # 根据得分判断性别
    if male_score > female_score:
        return 'male'
    elif female_score > male_score:
        return 'female'
    else:
        return 'unknown'

def analyze_gender_distribution(names):
    """
    分析姓名的性别分布
    
    Args:
        names (list): 姓名列表
        
    Returns:
        dict: 性别分布统计
    """
    gender_stats = {'male': 0, 'female': 0, 'unknown': 0}
    detailed_results = []
    
    for name in names:
        gender = analyze_gender_by_name(name)
        gender_stats[gender] += 1
        detailed_results.append({'name': name, 'gender': gender})
    
    return gender_stats, detailed_results

def save_detailed_results(detailed_results, output_file):
    """
    保存详细的性别分析结果
    
    Args:
        detailed_results (list): 详细结果列表
        output_file (str): 输出文件路径
    """
    try:
        with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['序号', '姓名', '推测性别'])
            
            for i, result in enumerate(detailed_results, 1):
                gender_cn = {'male': '男', 'female': '女', 'unknown': '未知'}
                writer.writerow([i, result['name'], gender_cn[result['gender']]])
                
        print(f"详细结果已保存到 {output_file}")
        
    except Exception as e:
        print(f"保存详细结果时发生错误：{e}")

def main():
    """主函数"""
    # 输入CSV文件路径
    input_csv = 'extracted_names.csv'
    
    # 输出详细结果文件路径
    output_csv = 'gender_analysis_results.csv'
    
    print("开始分析姓名性别分布...")
    
    # 加载姓名
    names = load_names_from_csv(input_csv)
    
    if not names:
        print("未能加载姓名数据")
        return
    
    print(f"总共加载了 {len(names)} 个姓名")
    
    # 分析性别分布
    gender_stats, detailed_results = analyze_gender_distribution(names)
    
    # 计算比例
    total = len(names)
    male_count = gender_stats['male']
    female_count = gender_stats['female']
    unknown_count = gender_stats['unknown']
    
    male_ratio = (male_count / total) * 100
    female_ratio = (female_count / total) * 100
    unknown_ratio = (unknown_count / total) * 100
    
    # 输出统计结果
    print("\n=== 性别分布统计 ===")
    print(f"总姓名数量: {total}")
    print(f"推测为男性: {male_count} 人 ({male_ratio:.1f}%)")
    print(f"推测为女性: {female_count} 人 ({female_ratio:.1f}%)")
    print(f"无法判断: {unknown_count} 人 ({unknown_ratio:.1f}%)")
    
    # 计算男女比例
    if female_count > 0:
        gender_ratio = male_count / female_count
        print(f"\n男女比例: {gender_ratio:.2f}:1")
    else:
        print("\n无法计算男女比例（女性数量为0）")
    
    # 保存详细结果
    save_detailed_results(detailed_results, output_csv)
    
    # 显示一些示例
    print("\n=== 男性姓名示例 ===")
    male_examples = [r['name'] for r in detailed_results if r['gender'] == 'male'][:10]
    for i, name in enumerate(male_examples, 1):
        print(f"{i}. {name}")
    
    print("\n=== 女性姓名示例 ===")
    female_examples = [r['name'] for r in detailed_results if r['gender'] == 'female'][:10]
    for i, name in enumerate(female_examples, 1):
        print(f"{i}. {name}")

if __name__ == "__main__":
    main() 