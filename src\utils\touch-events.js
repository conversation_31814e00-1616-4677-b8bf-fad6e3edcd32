/**
 * 移动端触摸事件处理工具
 * 解决300ms点击延迟问题
 */

// 判断是否是移动设备
export const isMobile = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

// 初始化FastClick
export const initFastClick = () => {
  // 检测是否是移动设备
  if (!isMobile()) return

  // 添加触摸事件处理
  document.body.addEventListener('touchstart', function() {
    // 这是一个空函数，但它能激活设备上的:active 伪类
  }, false)

  // 处理所有点击事件，防止重复触发
  const clickableElements = ['A', 'BUTTON', 'INPUT', 'SELECT', 'TEXTAREA', 'LABEL']
  
  document.addEventListener('touchstart', function(e) {
    if (!e.target) return
    
    // 检查是否点击了可点击元素或其父元素是可点击元素
    let el = e.target
    let isClickable = false
    
    // 检查元素本身或其父元素是否是可点击元素
    while (el && el !== document.body) {
      if (clickableElements.indexOf(el.tagName) > -1 || 
          el.getAttribute('role') === 'button' ||
          el.classList.contains('hamburger-container') || 
          el.classList.contains('sidebar-container')) {
        isClickable = true
        break
      }
      el = el.parentNode
    }
    
    if (!isClickable) return
    
    // 阻止默认行为，防止双击缩放
    e.preventDefault()
    
    // 模拟点击事件
    const touch = e.touches[0]
    const clickEvent = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window,
      screenX: touch.screenX,
      screenY: touch.screenY,
      clientX: touch.clientX,
      clientY: touch.clientY
    })
    
    // 延迟一点点时间，以确保其他事件处理程序已经完成
    setTimeout(() => {
      e.target.dispatchEvent(clickEvent)
    }, 10)
  }, false)
} 