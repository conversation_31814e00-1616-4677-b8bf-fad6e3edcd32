<template>
  <el-row :gutter="40" class="panel-group">
    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('machines')">
        <div class="card-panel-icon-wrapper icon-machines">
          <svg-icon icon-class="component" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            设备总数
          </div>
          <count-to
            :start-val="0"
            :end-val="stats.total_machines"
            :duration="2600"
            class="card-panel-num"
          />
        </div>
      </div>
    </el-col>

    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('revenue')">
        <div class="card-panel-icon-wrapper icon-revenue">
          <svg-icon icon-class="money" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            总共收入
          </div>
          <count-to
            :start-val="0"
            :end-val="stats.monthly_revenue"
            :duration="3200"
            class="card-panel-num"
          />
        </div>
      </div>
    </el-col>

    <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData('idle')">
        <div class="card-panel-icon-wrapper icon-idle">
          <svg-icon icon-class="shopping" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            空闲机器数
          </div>
          <count-to
            :start-val="0"
            :end-val="stats.idle_machines"
            :duration="3200"
            class="card-panel-num"
          />
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import CountTo from 'vue-count-to'
import { getDashboardStats } from '@/api/dashboard'

export default {
  components: {
    CountTo
  },
  data() {
    return {
      stats: {
        total_machines: 0,
        monthly_revenue: 0,
        idle_machines: 0
      },
      loading: false
    }
  },
  mounted() {
    this.fetchStats()
  },
  methods: {
    async fetchStats() {
      this.loading = true
      try {
        const response = await getDashboardStats()
        this.stats = response || this.stats
      } catch (error) {
        console.error('获取统计数据失败:', error)
        this.$message.error('获取统计数据失败')
      } finally {
        this.loading = false
      }
    },
    handleSetLineChartData(type) {
      this.$emit('handleSetLineChartData', type)
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 18px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-machines {
        background: #40c9c6;
      }

      .icon-revenue {
        background: #f4516c;
      }

      .icon-idle {
        background: #36a3f7;
      }
    }

    .icon-machines {
      color: #40c9c6;
    }

    .icon-revenue {
      color: #f4516c;
    }

    .icon-idle {
      color: #36a3f7;
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .panel-group {
    margin-top: 12px;
    
    .card-panel-col {
      margin-bottom: 15px;
    }
    
    .card-panel {
      height: 100px;
      
      .card-panel-icon-wrapper {
        margin: 10px 0 0 10px;
        padding: 12px;
      }
      
      .card-panel-icon {
        font-size: 36px;
      }
      
      .card-panel-description {
        margin: 20px;
        
        .card-panel-text {
          font-size: 14px;
          margin-bottom: 8px;
        }
        
        .card-panel-num {
          font-size: 18px;
        }
      }
    }
  }
}

// 小屏幕手机适配
@media (max-width: 480px) {
  .panel-group {
    margin-top: 10px;
    
    .card-panel-col {
      margin-bottom: 10px;
    }
    
    .card-panel {
      height: 100px;
      
      .card-panel-icon-wrapper {
        margin: 8px 0 0 8px;
        padding: 10px;
      }
      
      .card-panel-icon {
        font-size: 30px;
      }
      
      .card-panel-description {
        margin: 15px;
        
        .card-panel-text {
          font-size: 12px;
          margin-bottom: 6px;
        }
        
        .card-panel-num {
          font-size: 16px;
        }
      }
    }
  }
}

@media (max-width: 550px) {
  .card-panel-description {
    display: flex;
    flex-direction: column;
    align-items: center;
    float: none !important;
    margin: 8px auto !important;
    text-align: center;
    width: 100%;
    padding-top: 5px;
    
    .card-panel-text {
      font-size: 12px !important;
      margin-bottom: 5px !important;
      white-space: nowrap;
    }
    
    .card-panel-num {
      font-size: 16px !important;
    }
  }

  .card-panel-icon-wrapper {
    float: none !important;
    margin: 8px auto 0 !important;
    display: flex;
    justify-content: center;
    padding: 10px !important;

    .svg-icon {
      margin: 0 auto !important;
      float: none !important;
      font-size: 28px !important;
    }
  }
  
  .card-panel {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 5px 0;
  }
}

// 超小屏幕手机适配
@media (max-width: 360px) {
  .panel-group {
    .card-panel {
      height: 110px;
    }
    
    .el-col {
      width: 100% !important;
      flex: 0 0 100%;
    }
    
    .card-panel-description {
      .card-panel-text {
        font-size: 14px !important;
      }
      
      .card-panel-num {
        font-size: 18px !important;
      }
    }
  }
}
</style>
