<template>
  <div class="app-container">
    <div class="sn-input-container">
      <el-input
        v-model="snCode"
        placeholder="请扫入SN码"
        maxlength="15"
        style="width: 300px;"
        @input="handleSNInput"
      />
    </div>
    <el-dialog
      title="请选择操作"
      :visible.sync="dialogVisible"
      width="400px"
      @close="resetSN"
    >
      <div class="dialog-btns">
        <el-button type="primary" @click="handleAction('in')">入库</el-button>
        <el-button type="success" @click="handleAction('out')">出库</el-button>
        <!-- <el-button type="warning" @click="handleAction('new')">新购</el-button> -->
        <!-- <el-button type="info" @click="handleAction('replace')">换新</el-button> -->
      </div>
    </el-dialog>
    <!-- 租赁单选择弹窗 -->
    <el-dialog
      title="选择入库租赁单"
      :visible.sync="rentalDialogVisible"
      width="800px"
      @close="resetSN"
    >
      <div v-if="loading" style="text-align:center;padding:40px 0;">
        <el-spinner />
      </div>
      <div v-else-if="rentals.length === 0" style="text-align:center;padding:40px 0;">
        <p>未找到与该SN码相关的租赁单</p>
      </div>
      <div v-else>
        <el-table
          :data="rentals"
          style="width:100%;margin-bottom:16px;"
          @row-click="selectRental"
          highlight-current-row
          :row-class-name="row => row.id === selectedRentalId ? 'selected-row' : ''"
        >
          <el-table-column prop="id" label="单号" width="160" />
          <el-table-column prop="customer_name" label="客户" width="120" />
          <el-table-column prop="device_number" label="设备编号" width="120" />
          <el-table-column prop="start_date" label="开始日期" width="120" />
          <el-table-column prop="end_date" label="结束日期" width="120" />
          <el-table-column prop="send_status" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.send_status)">{{ scope.row.send_status }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
        <div style="text-align:right;">
          <el-button @click="rentalDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmInStorage" :disabled="!selectedRentalId">确认入库</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 出库租赁单选择弹窗 -->
    <el-dialog
      title="选择出库租赁单"
      :visible.sync="outRentalDialogVisible"
      width="800px"
      @close="resetSN"
    >
      <div v-if="loading" style="text-align:center;padding:40px 0;">
        <el-spinner />
      </div>
      <div v-else-if="outRentals.length === 0" style="text-align:center;padding:40px 0;">
        <p>未找到与该SN码相关的待发货租赁单</p>
      </div>
      <div v-else>
        <el-table
          :data="outRentals"
          style="width:100%;margin-bottom:16px;"
          @row-click="selectOutRental"
          highlight-current-row
          :row-class-name="row => row.id === selectedOutRentalId ? 'selected-row' : ''"
        >
          <el-table-column prop="id" label="单号" width="160" />
          <el-table-column prop="customer_name" label="客户" width="120" />
          <el-table-column prop="device_number" label="设备编号" width="120" />
          <el-table-column prop="start_date" label="开始日期" width="120" />
          <el-table-column prop="end_date" label="结束日期" width="120" />
          <el-table-column prop="send_status" label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.send_status)">{{ scope.row.send_status }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 快递单号输入区域 -->
        <div v-if="selectedOutRentalId" style="margin-top: 20px; padding: 16px; background: #f5f7fa; border-radius: 4px;">
          <div style="margin-bottom: 12px;">
            <p style="margin: 0; font-weight: bold;">出库信息确认</p>
            <p style="margin: 4px 0; color: #666;">客户：{{ selectedOutRentalInfo.customer_name }}</p>
            <p style="margin: 4px 0; color: #666;">设备编号：{{ selectedOutRentalInfo.device_number }}</p>
            <p style="margin: 4px 0; color: #666;">SN码：{{ snCode }}</p>
          </div>
          <div style="margin-bottom: 16px;">
            <label style="display: block; margin-bottom: 8px; font-weight: bold;">快递单号：</label>
            <el-input
              v-model="trackingNumber"
              placeholder="请输入快递单号"
              style="width: 100%;"
              @keyup.enter="confirmOutStorage"
            />
          </div>
        </div>
        
        <div style="text-align:right; margin-top: 16px;">
          <el-button @click="outRentalDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmOutStorage" :disabled="!selectedOutRentalId || !trackingNumber.trim()">完成出库</el-button>
        </div>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getRecentRentals, getPendingRentals, returnDevice, shipDevice } from '@/api/rental'

export default {
  name: 'InventoryIndex',
  data() {
    return {
      snCode: '',
      dialogVisible: false,
      // 入库租赁单相关数据
      rentalDialogVisible: false,
      rentals: [],
      selectedRentalId: null,
      // 出库租赁单相关数据
      outRentalDialogVisible: false,
      outRentals: [],
      selectedOutRentalId: null,
      selectedOutRentalInfo: {
        customer_name: '',
        device_number: '',
        id: null
      },
      // 快递单号
      trackingNumber: '',
      loading: false
    }
  },
  methods: {
    handleSNInput(val) {
      // SN码长度为14位或15位时弹窗
      if (val.length === 14 || val.length === 15) {
        this.dialogVisible = true
      }
    },
    handleAction(action) {
      if (action === 'in') {
        // 入库，弹出租赁单选择弹窗
        this.fetchRecentRentals()
        this.dialogVisible = false
        this.rentalDialogVisible = true
      } else if (action === 'out') {
        // 出库，弹出待发货租赁单选择弹窗
        this.fetchPendingRentals()
        this.dialogVisible = false
        this.outRentalDialogVisible = true
      } else {
        this.$message.success(`操作：${this.getActionName(action)}，SN码：${this.snCode}`)
        this.dialogVisible = false
        this.resetSN()
      }
    },
    // 获取最近租赁单（用于入库）
    fetchRecentRentals() {
      this.loading = true
      getRecentRentals({ limit: 10, sn_code: this.snCode })
        .then(response => {
          console.log('获取到的租赁单数据:', response)
          // 检查response格式，确保正确获取数据
          if (Array.isArray(response)) {
            this.rentals = response
          } else if (response.data && Array.isArray(response.data)) {
            this.rentals = response.data
          } else {
            this.rentals = []
            console.error('返回的租赁单数据格式不正确:', response)
          }
          
          this.selectedRentalId = null
          
          // 如果只有一条记录，自动选择
          if (this.rentals.length === 1) {
            this.selectedRentalId = this.rentals[0].id
          }
        })
        .catch(error => {
          console.error('获取租赁单失败:', error)
          this.$message.error('获取租赁单列表失败')
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 获取待发货租赁单（用于出库）
    fetchPendingRentals() {
      this.loading = true
      getPendingRentals({ limit: 10, sn_code: this.snCode })
        .then(response => {
          console.log('获取到的待发货租赁单数据:', response)
          // 检查response格式，确保正确获取数据
          if (Array.isArray(response)) {
            this.outRentals = response
          } else if (response.data && Array.isArray(response.data)) {
            this.outRentals = response.data
          } else {
            this.outRentals = []
            console.error('返回的待发货租赁单数据格式不正确:', response)
          }
          
          this.selectedOutRentalId = null
          
          // 如果只有一条记录，自动选择
          if (this.outRentals.length === 1) {
            this.selectedOutRentalId = this.outRentals[0].id
            this.$set(this, 'selectedOutRentalInfo', {
              id: this.outRentals[0].id,
              customer_name: this.outRentals[0].customer_name,
              device_number: this.outRentals[0].device_number,
              start_date: this.outRentals[0].start_date,
              end_date: this.outRentals[0].end_date,
              send_status: this.outRentals[0].send_status
            })
          }
        })
        .catch(error => {
          console.error('获取待发货租赁单失败:', error)
          this.$message.error('获取待发货租赁单列表失败')
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 选择租赁单（入库）
    selectRental(row) {
      this.selectedRentalId = row.id
    },
    // 选择租赁单（出库）
    selectOutRental(row) {
      this.selectedOutRentalId = row.id
      this.$set(this, 'selectedOutRentalInfo', {
        id: row.id,
        customer_name: row.customer_name,
        device_number: row.device_number,
        start_date: row.start_date,
        end_date: row.end_date,
        send_status: row.send_status
      })
    },

    // 确认入库
    confirmInStorage() {
      if (!this.selectedRentalId) {
        this.$message.warning('请选择一笔租赁单')
        return
      }
      
      this.loading = true
      returnDevice(this.selectedRentalId, this.snCode)
        .then(response => {
          const rental = this.rentals.find(r => r.id === this.selectedRentalId)
          this.$message.success(`入库成功！SN码：${this.snCode}，关联租赁单：${rental.id}（${rental.customer_name}）`)
          this.rentalDialogVisible = false
          this.resetSN()
        })
        .catch(error => {
          console.error('入库操作失败:', error)
          if (error.response && error.response.data && error.response.data.detail) {
            this.$message.error(error.response.data.detail)
          } else {
            this.$message.error('入库操作失败')
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 确认出库
    confirmOutStorage() {
      if (!this.selectedOutRentalId) {
        this.$message.warning('请选择一笔租赁单')
        return
      }
      
      if (!this.trackingNumber.trim()) {
        this.$message.warning('请输入快递单号')
        return
      }
      
      this.loading = true
      shipDevice(this.selectedOutRentalId, this.snCode, this.trackingNumber.trim())
        .then(response => {
          this.$message.success(`出库成功！SN码：${this.snCode}，关联租赁单：${this.selectedOutRentalInfo.id}（${this.selectedOutRentalInfo.customer_name}），快递单号：${this.trackingNumber}`)
          this.outRentalDialogVisible = false
          this.resetSN()
        })
        .catch(error => {
          console.error('出库操作失败:', error)
          if (error.response && error.response.data && error.response.data.detail) {
            this.$message.error(error.response.data.detail)
          } else {
            this.$message.error('出库操作失败')
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    getActionName(action) {
      switch(action) {
        case 'in': return '入库'
        case 'out': return '出库'
        case 'new': return '新购设备'
        case 'replace': return '换新设备'
        default: return ''
      }
    },
    getStatusType(status) {
      switch(status) {
        case '已发货': return 'warning'
        case '已归还': return 'success'
        case '待发货': return 'info'
        case '未发货': return 'info'
        default: return ''
      }
    },
    resetSN() {
      this.snCode = ''
      this.selectedRentalId = null
      this.selectedOutRentalId = null
      this.selectedOutRentalInfo = {
        customer_name: '',
        device_number: '',
        id: null
      }
      this.rentals = []
      this.outRentals = []
    },

    resetOutStorage() {
      this.trackingNumber = ''
      this.selectedOutRentalId = null
      this.selectedOutRentalInfo = {
        customer_name: '',
        device_number: '',
        id: null
      }
      this.outRentals = []
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .sn-input-container {
    margin: 40px 0 20px 0;
  }
  .dialog-btns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    align-items: center;
    justify-items: center;
    margin-top: 20px;
  }
}
.selected-row {
  background: #e0f7fa !important;
}
</style> 