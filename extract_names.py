#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
姓名提取程序
从姓名.txt文件中提取姓名并输出为CSV格式
"""

import re
import csv
import os

def extract_names_from_file(file_path):
    """
    从文件中提取姓名
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        list: 提取的姓名列表
    """
    names = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
        # 按行分割
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # 跳过空行和标题行
            if not line or line in ['身份证号', '']:
                continue
                
            # 使用正则表达式匹配姓名
            # 匹配中文姓名（2-4个中文字符）+ 可选的空格 + 可选的数字/字母组合
            pattern = r'^([·\u4e00-\u9fa5]{2,10})(?:\s+[0-9X]+)?$'
            
            match = re.match(pattern, line)
            if match:
                name = match.group(1).strip()
                # 过滤掉一些明显不是姓名的内容
                if name and not re.match(r'^\d+$', name):
                    names.append(name)
            else:
                # 尝试其他可能的格式
                # 处理包含特殊字符的姓名，如"李家麒/李琛"
                if '/' in line:
                    parts = line.split('/')
                    for part in parts:
                        part = part.strip()
                        if re.match(r'^[·\u4e00-\u9fa5]{2,10}$', part):
                            names.append(part)
                # 处理单独的姓名行
                elif re.match(r'^[·\u4e00-\u9fa5]{2,10}$', line):
                    names.append(line)
    
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return []
    except Exception as e:
        print(f"读取文件时发生错误：{e}")
        return []
    
    # 去除重复姓名并保持原有顺序
    unique_names = []
    seen = set()
    for name in names:
        if name not in seen:
            unique_names.append(name)
            seen.add(name)
    
    return unique_names

def save_to_csv(names, output_file):
    """
    将姓名列表保存为CSV文件
    
    Args:
        names (list): 姓名列表
        output_file (str): 输出文件路径
    """
    try:
        with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            
            # 写入表头
            writer.writerow(['序号', '姓名'])
            
            # 写入数据
            for i, name in enumerate(names, 1):
                writer.writerow([i, name])
                
        print(f"成功保存 {len(names)} 个姓名到 {output_file}")
        
    except Exception as e:
        print(f"保存CSV文件时发生错误：{e}")

def main():
    """主函数"""
    # 输入文件路径
    input_file = 'backend/姓名.txt'
    
    # 输出文件路径
    output_file = 'extracted_names.csv'
    
    print("开始提取姓名...")
    
    # 提取姓名
    names = extract_names_from_file(input_file)
    
    if names:
        print(f"成功提取 {len(names)} 个不重复的姓名")
        
        # 保存为CSV
        save_to_csv(names, output_file)
        
        # 显示前10个姓名作为预览
        print("\n前10个姓名预览：")
        for i, name in enumerate(names[:10], 1):
            print(f"{i}. {name}")
            
        if len(names) > 10:
            print(f"... 还有 {len(names) - 10} 个姓名")
    else:
        print("未提取到任何姓名")

if __name__ == "__main__":
    main() 