from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import and_, extract, or_
from database.database import get_db
from models.rental import Rental
from models.machine import Machine
from models.user import User
from app.schemas import (
    RentalCreate, Rental as RentalSchema, RentalUpdate,
    AutoScheduleConfig, AutoSchedulePreview, ScheduleChange, ScheduleConfirmData,
    DeviceAllocationConfig, DeviceAllocationPreview, DeviceChange, DeviceAllocationConfirmData
)
from app.auth import get_current_user
from datetime import datetime, date, timedelta
import calendar
from sqlalchemy import case

router = APIRouter(prefix="/rentals", tags=["租赁管理"])

@router.post("/", response_model=RentalSchema)
def create_rental(
    rental: RentalCreate, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 创建租赁时自动设置user_id
    rental_data = rental.dict()
    rental_data["user_id"] = current_user.id
    db_rental = Rental(**rental_data)
    db.add(db_rental)
    db.commit()
    db.refresh(db_rental)
    return db_rental

@router.get("/", response_model=List[RentalSchema])
def read_rentals(
    skip: int = 0, 
    limit: int = 100,
    sort: str = None,
    search: str = None,
    status: str = None,
    exclude_status: str = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 调试输出所有请求参数
    print(f"请求参数: skip={skip}, limit={limit}, sort={sort}, search={search}, status={status}, exclude_status={exclude_status}")
    
    # 创建基本查询
    query = db.query(Rental).filter(Rental.user_id == current_user.id)
    
    # 添加搜索功能
    if search:
        query = query.filter(
            or_(
                Rental.device_number.ilike(f"%{search}%"),
                Rental.customer_name.ilike(f"%{search}%")
            )
        )
    
    # 添加状态筛选
    if status:
        query = query.filter(Rental.send_status == status)
    
    # 添加排除状态筛选
    if exclude_status:
        query = query.filter(Rental.send_status != exclude_status)
    
    # 添加排序功能
    if sort:
        print(f"尝试排序，字段: {sort}")
        # 检查字段是否存在于Rental模型中
        valid_sort_fields = [column.name for column in Rental.__table__.columns]
        print(f"有效的排序字段: {valid_sort_fields}")
        
        # 处理降序排序（以"-"开头）
        if sort.startswith('-'):
            sort_field = sort[1:]
            print(f"降序排序字段: {sort_field}")
            if sort_field in valid_sort_fields:
                query = query.order_by(getattr(Rental, sort_field).desc())
                print(f"应用降序排序: {sort_field}")
            else:
                print(f"无效的排序字段: {sort_field}")
        else:
            # 升序排序
            print(f"升序排序字段: {sort}")
            if sort in valid_sort_fields:
                query = query.order_by(getattr(Rental, sort).asc())
                print(f"应用升序排序: {sort}")
            else:
                print(f"无效的排序字段: {sort}")
    else:
        # 默认按照ID倒序排序
        query = query.order_by(Rental.id.desc())
        print("应用默认排序: id 倒序")
    
    # 分页
    rentals = query.offset(skip).limit(limit).all()
    
    # 输出返回的记录数
    print(f"返回记录数: {len(rentals)}")
    if rentals:
        print(f"第一条记录: id={rentals[0].id}, start_date={rentals[0].start_date}, send_status={rentals[0].send_status}")
    
    return rentals

@router.get("/recent", response_model=List[RentalSchema])
def get_recent_rentals(
    limit: int = 10,
    sn_code: str = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取最近的租赁单列表，如果提供SN码则查询与该SN码相关的租赁订单"""
    # 添加日志记录请求参数
    print(f"[recent] 请求参数: limit={limit}, sn_code={sn_code}, user_id={current_user.id}")
    
    # 先查询所有设备并记录日志
    all_machines = db.query(Machine).filter(Machine.user_id == current_user.id).all()
    print(f"[recent] 当前用户拥有的设备总数: {len(all_machines)}")
    for machine in all_machines:
        print(f"[recent] 设备信息: device_number={machine.device_number}, sn_code={machine.sn_code}")
    
    # 如果提供了SN码，查询与该SN码关联的设备编号
    device_number = None
    if sn_code:
        machine = db.query(Machine).filter(
            and_(
                Machine.sn_code == sn_code,
                Machine.user_id == current_user.id
            )
        ).first()
        
        if machine:
            device_number = machine.device_number
            print(f"[recent] 找到设备: sn_code={sn_code} -> device_number={device_number}")
        else:
            print(f"[recent] 未找到设备: sn_code={sn_code}")
    
    # 构建查询
    query = db.query(Rental).filter(Rental.user_id == current_user.id)
    
    # 如果找到了设备编号，则根据设备编号过滤
    if device_number:
        query = query.filter(Rental.device_number == device_number)
        # 优先显示已发货但未归还的订单
        query = query.order_by(
            # 先按状态排序（已发货的排在前面）
            case(
                (Rental.send_status == "已发货", 0),
                else_=1
            ),
            # 然后按创建时间倒序
            Rental.created_at.desc()
        )
        print(f"[recent] 按设备编号过滤: device_number={device_number}")
    else:
        # 如果提供了SN码但没找到对应设备，返回空列表
        if sn_code:
            print(f"[recent] 提供了SN码但未找到对应设备，返回空列表")
            return []
        
        # 没有提供SN码时，先查询已发货但未归还的订单
        not_returned_rentals = query.filter(Rental.send_status == "已发货").order_by(Rental.created_at.desc()).limit(limit).all()
        print(f"[recent] 查询未归还订单: 找到 {len(not_returned_rentals)} 条记录")
        
        # 如果未归还订单数量不足，补充其他最近订单
        if len(not_returned_rentals) < limit:
            remaining = limit - len(not_returned_rentals)
            # 排除已查询的未归还订单
            not_returned_ids = [r.id for r in not_returned_rentals]
            other_rentals = query.filter(
                Rental.id.notin_(not_returned_ids) if not_returned_ids else True
            ).order_by(Rental.created_at.desc()).limit(remaining).all()
            
            print(f"[recent] 补充其他订单: 找到 {len(other_rentals)} 条记录")
            
            # 合并结果
            result = not_returned_rentals + other_rentals
            print(f"[recent] 返回结果: 总计 {len(result)} 条记录")
            return result
        else:
            print(f"[recent] 返回结果: 总计 {len(not_returned_rentals)} 条记录")
            return not_returned_rentals
    
    # 应用分页
    rentals = query.limit(limit).all()
    print(f"[recent] 返回结果: 总计 {len(rentals)} 条记录")
    if rentals and len(rentals) > 0:
        print(f"[recent] 第一条记录: id={rentals[0].id}, device_number={rentals[0].device_number}, send_status={rentals[0].send_status}")
    
    return rentals

@router.get("/pending", response_model=List[RentalSchema])
def get_pending_rentals(
    limit: int = 10,
    sn_code: str = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取待发货的租赁单列表，如果提供SN码则查询与该SN码相关的待发货订单"""
    # 添加日志记录请求参数
    print(f"[pending] 请求参数: limit={limit}, sn_code={sn_code}, user_id={current_user.id}")
    
    # 如果提供了SN码，查询与该SN码关联的设备编号
    device_number = None
    if sn_code:
        machine = db.query(Machine).filter(
            and_(
                Machine.sn_code == sn_code,
                Machine.user_id == current_user.id
            )
        ).first()
        
        if machine:
            device_number = machine.device_number
            print(f"[pending] 找到设备: sn_code={sn_code} -> device_number={device_number}")
        else:
            print(f"[pending] 未找到设备: sn_code={sn_code}")
    
    # 构建查询 - 查询待发货的订单
    query = db.query(Rental).filter(
        and_(
            Rental.user_id == current_user.id,
            or_(
                Rental.send_status == "待发货",
                Rental.send_status == "未发货",
                Rental.send_status.is_(None),
                Rental.send_status == ""
            )
        )
    )
    
    # 如果找到了设备编号，则根据设备编号过滤
    if device_number:
        query = query.filter(Rental.device_number == device_number)
        print(f"[pending] 按设备编号过滤: device_number={device_number}")
    else:
        # 如果提供了SN码但没找到对应设备，返回空列表
        if sn_code:
            print(f"[pending] 提供了SN码但未找到对应设备，返回空列表")
            return []
    
    # 按创建时间倒序排列
    query = query.order_by(Rental.created_at.desc())
    
    # 应用分页
    rentals = query.limit(limit).all()
    print(f"[pending] 返回结果: 总计 {len(rentals)} 条记录")
    if rentals and len(rentals) > 0:
        print(f"[pending] 第一条记录: id={rentals[0].id}, device_number={rentals[0].device_number}, send_status={rentals[0].send_status}")
    
    return rentals

@router.get("/{rental_id}", response_model=RentalSchema)
def read_rental(
    rental_id: int, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 只返回当前用户的租赁记录
    rental = db.query(Rental).filter(
        Rental.id == rental_id,
        Rental.user_id == current_user.id
    ).first()
    if rental is None:
        raise HTTPException(status_code=404, detail="租赁记录不存在")
    return rental

@router.put("/{rental_id}", response_model=RentalSchema)
def update_rental(
    rental_id: int, 
    rental_update: RentalUpdate, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 只更新当前用户的租赁记录
    rental = db.query(Rental).filter(
        Rental.id == rental_id,
        Rental.user_id == current_user.id
    ).first()
    if rental is None:
        raise HTTPException(status_code=404, detail="租赁记录不存在")
    
    update_data = rental_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(rental, field, value)
    
    db.commit()
    db.refresh(rental)
    return rental

@router.delete("/{rental_id}")
def delete_rental(
    rental_id: int, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 只删除当前用户的租赁记录
    rental = db.query(Rental).filter(
        Rental.id == rental_id,
        Rental.user_id == current_user.id
    ).first()
    if rental is None:
        raise HTTPException(status_code=404, detail="租赁记录不存在")
    
    db.delete(rental)
    db.commit()
    return {"message": "租赁记录删除成功"}

@router.get("/by-device/{device_number}", response_model=List[RentalSchema])
def read_rentals_by_device(
    device_number: str, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # 只返回当前用户的设备租赁记录
    rentals = db.query(Rental).filter(
        Rental.device_number == device_number,
        Rental.user_id == current_user.id
    ).all()
    return rentals



@router.get("/schedule/{year}/{month}")
def get_schedule_data(
    year: int,
    month: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取指定年月的排单数据"""
    
    # 获取该月的第一天和最后一天
    first_day = date(year, month, 1)
    last_day = date(year, month, calendar.monthrange(year, month)[1])
    
    # 获取当前用户的所有设备
    machines = db.query(Machine).filter(Machine.user_id == current_user.id).all()
    
    # 获取该月当前用户有租赁记录的所有租赁信息
    rentals = db.query(Rental).filter(
        and_(
            Rental.start_date <= last_day,
            Rental.end_date >= first_day,
            Rental.user_id == current_user.id
        )
    ).all()
    
    # 构建排单数据结构
    schedule_data = {
        "year": year,
        "month": month,
        "days_in_month": calendar.monthrange(year, month)[1],
        "devices": []
    }
    
    for machine in machines:
        device_data = {
            "device_number": machine.device_number,
            "sn_code": machine.sn_code,
            "rental_days": []
        }
        
        # 获取该设备在这个月的所有租赁记录
        device_rentals = [r for r in rentals if r.device_number == machine.device_number]
        
        # 为每一天标记租赁状态
        for day in range(1, calendar.monthrange(year, month)[1] + 1):
            current_date = date(year, month, day)
            is_rented = False
            rental_info = None
            
            for rental in device_rentals:
                if rental.start_date <= current_date <= rental.end_date:
                    is_rented = True
                    rental_info = {
                        "id": rental.id,
                        "customer_name": rental.customer_name,
                        "start_date": rental.start_date.isoformat(),
                        "end_date": rental.end_date.isoformat(),
                        "rent_amount": rental.rent_amount,
                        "send_status": rental.send_status,
                        "send_date": rental.send_date.isoformat() if rental.send_date else None,
                        "address": rental.address,
                        "xianyu_order": rental.xianyu_order,
                        "send_tracking": rental.send_tracking,
                        "receive_tracking": rental.receive_tracking,
                        "accessories_note": rental.accessories_note,
                        "note": rental.note,
                        "created_at": rental.created_at.isoformat() if rental.created_at else None
                    }
                    break
            
            device_data["rental_days"].append({
                "day": day,
                "date": current_date.isoformat(),
                "is_rented": is_rented,
                "rental_info": rental_info
            })
        
        schedule_data["devices"].append(device_data)
    
    return schedule_data


class AutoScheduler:
    """自动排单核心算法类"""
    
    def __init__(self, db: Session, user_id: int, config: AutoScheduleConfig):
        self.db = db
        self.user_id = user_id
        self.config = config
    
    def generate_schedule_preview(self) -> AutoSchedulePreview:
        """生成自动排单预览"""
        # 1. 获取待排单订单
        pending_rentals = self._get_pending_rentals()
        
        if not pending_rentals:
            return AutoSchedulePreview(
                summary={
                    "total_orders": 0,
                    "success_count": 0,
                    "conflict_count": 0,
                    "optimized_count": 0
                },
                schedule_changes=[]
            )
        
        # 2. 获取设备占用情况
        device_schedules = self._get_device_schedules()
        
        # 3. 执行排单算法
        schedule_changes = []
        for rental in pending_rentals:
            change = self._calculate_optimal_schedule(rental, device_schedules)
            schedule_changes.append(change)
            
            # 更新设备占用状态（用于后续订单计算）
            if change.status == "success":
                device_number = change.device_number
                if device_number not in device_schedules:
                    device_schedules[device_number] = []
                device_schedules[device_number].append({
                    'start_date': change.new_start_date,
                    'end_date': change.new_end_date,
                    'rental_id': change.rental_id
                })
        
        # 4. 生成统计摘要
        summary = self._generate_summary(schedule_changes)
        
        return AutoSchedulePreview(
            summary=summary,
            schedule_changes=schedule_changes
        )
    
    def _get_pending_rentals(self) -> List[Rental]:
        """获取未发货的租赁记录"""
        return self.db.query(Rental).filter(
            and_(
                Rental.user_id == self.user_id,
                or_(
                    Rental.send_status == "未发货",
                    Rental.send_status.is_(None),
                    Rental.send_status == ""
                )
            )
        ).order_by(Rental.start_date, Rental.created_at).all()
    
    def _get_device_schedules(self) -> Dict[str, List[Dict]]:
        """获取所有设备的当前占用情况"""
        # 获取当前日期之后的所有已排单租赁记录
        today = date.today()
        future_rentals = self.db.query(Rental).filter(
            and_(
                Rental.user_id == self.user_id,
                Rental.end_date >= today,
                Rental.send_status != "未发货"
            )
        ).all()
        
        device_schedules = {}
        for rental in future_rentals:
            if rental.device_number:
                if rental.device_number not in device_schedules:
                    device_schedules[rental.device_number] = []
                device_schedules[rental.device_number].append({
                    'start_date': rental.start_date,
                    'end_date': rental.end_date,
                    'rental_id': rental.id
                })
        
        # 按开始时间排序
        for device_number in device_schedules:
            device_schedules[device_number].sort(key=lambda x: x['start_date'])
        
        return device_schedules
    
    def _calculate_optimal_schedule(self, rental: Rental, device_schedules: Dict) -> ScheduleChange:
        """计算单个租赁记录的最优排单时间"""
        device_number = rental.device_number
        rental_duration = rental.end_date - rental.start_date
        
        # 如果没有指定设备编号，返回警告
        if not device_number:
            return ScheduleChange(
                rental_id=rental.id,
                device_number="未指定",
                customer_name=rental.customer_name,
                original_start_date=rental.start_date,
                original_end_date=rental.end_date,
                new_start_date=rental.start_date,
                new_end_date=rental.end_date,
                status="warning",
                reason="未指定设备编号，无法自动排单",
                conflicts=["设备编号为空"]
            )
        
        # 获取该设备的占用情况
        device_schedule = device_schedules.get(device_number, [])
        
        # 计算最早可用时间
        optimal_start = self._find_earliest_available_time(
            device_schedule, rental_duration, rental.start_date
        )
        optimal_end = optimal_start + rental_duration
        
        # 检查是否有变化
        if optimal_start == rental.start_date and optimal_end == rental.end_date:
            status = "success"
            reason = "时间无需调整"
        else:
            status = "success"
            days_moved = (optimal_start - rental.start_date).days
            if days_moved > 0:
                reason = f"设备{device_number}占用，延后{days_moved}天"
            else:
                reason = f"设备{device_number}空闲，提前{abs(days_moved)}天"
        
        # 检查是否超出最大调整范围
        days_diff = abs((optimal_start - rental.start_date).days)
        if days_diff > self.config.max_reschedule_days:
            status = "conflict"
            reason = f"需要调整{days_diff}天，超出最大调整范围({self.config.max_reschedule_days}天)"
        
        return ScheduleChange(
            rental_id=rental.id,
            device_number=device_number,
            customer_name=rental.customer_name,
            original_start_date=rental.start_date,
            original_end_date=rental.end_date,
            new_start_date=optimal_start,
            new_end_date=optimal_end,
            status=status,
            reason=reason,
            conflicts=[]
        )
    
    def _find_earliest_available_time(self, device_schedule: List[Dict], duration: timedelta, preferred_start: date) -> date:
        """找到设备最早可用的时间段"""
        today = date.today()
        earliest_start = max(today, preferred_start)
        
        if not device_schedule:
            return earliest_start
        
        # 检查是否可以在第一个预订之前安排
        first_booking = device_schedule[0]
        if earliest_start + duration + timedelta(days=self.config.buffer_days) <= first_booking['start_date']:
            return earliest_start
        
        # 在现有预订之间寻找空隙
        for i in range(len(device_schedule) - 1):
            current_end = device_schedule[i]['end_date']
            next_start = device_schedule[i + 1]['start_date']
            
            potential_start = max(
                earliest_start,
                current_end + timedelta(days=self.config.buffer_days)
            )
            potential_end = potential_start + duration
            
            if potential_end + timedelta(days=self.config.buffer_days) <= next_start:
                return potential_start
        
        # 如果没有找到合适的空隙，安排在最后一个预订之后
        last_booking = device_schedule[-1]
        return max(
            earliest_start,
            last_booking['end_date'] + timedelta(days=self.config.buffer_days)
        )
    
    def _generate_summary(self, schedule_changes: List[ScheduleChange]) -> Dict[str, int]:
        """生成排单统计摘要"""
        total_orders = len(schedule_changes)
        success_count = len([c for c in schedule_changes if c.status == "success"])
        conflict_count = len([c for c in schedule_changes if c.status == "conflict"])
        warning_count = len([c for c in schedule_changes if c.status == "warning"])
        optimized_count = len([
            c for c in schedule_changes 
            if c.status == "success" and (
                c.new_start_date != c.original_start_date or 
                c.new_end_date != c.original_end_date
            )
        ])
        
        return {
            "total_orders": total_orders,
            "success_count": success_count,
            "conflict_count": conflict_count,
            "warning_count": warning_count,
            "optimized_count": optimized_count
        }


@router.post("/auto-schedule/preview", response_model=AutoSchedulePreview)
def preview_auto_schedule(
    config: AutoScheduleConfig = AutoScheduleConfig(),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """生成自动排单预览"""
    scheduler = AutoScheduler(db, current_user.id, config)
    return scheduler.generate_schedule_preview()


@router.post("/auto-schedule/confirm")
def confirm_auto_schedule(
    confirm_data: ScheduleConfirmData,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """确认并执行自动排单"""
    try:
        success_count = 0
        error_count = 0
        error_details = []
        
        for change in confirm_data.schedule_changes:
            if change.status == "success":
                # 更新租赁记录
                rental = db.query(Rental).filter(
                    and_(
                        Rental.id == change.rental_id,
                        Rental.user_id == current_user.id
                    )
                ).first()
                
                if rental:
                    rental.start_date = change.new_start_date
                    rental.end_date = change.new_end_date
                    
                    # 如果设置了自动更新发货状态
                    if confirm_data.auto_update_send_status and not rental.send_status:
                        rental.send_status = "未发货"
                    
                    success_count += 1
                else:
                    error_count += 1
                    error_details.append(f"租赁记录ID {change.rental_id} 不存在")
        
        db.commit()
        
        return {
            "message": "自动排单执行完成",
            "success_count": success_count,
            "error_count": error_count,
            "error_details": error_details
        }
    
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"自动排单执行失败: {str(e)}") 


class AutoDeviceAllocator:
    """自动设备分配核心算法类"""
    
    def __init__(self, db: Session, user_id: int, config: DeviceAllocationConfig):
        self.db = db
        self.user_id = user_id
        self.config = config
    
    def generate_allocation_preview(self) -> DeviceAllocationPreview:
        """生成设备分配预览"""
        # 1. 获取待分配订单（客户时间保持不变）
        pending_rentals = self._get_pending_rentals()
        
        if not pending_rentals:
            return DeviceAllocationPreview(
                summary={
                    "total_orders": 0,
                    "success_count": 0,
                    "conflict_count": 0,
                    "optimized_count": 0
                },
                device_changes=[]
            )
        
        # 2. 获取所有设备的占用情况
        device_schedules = self._get_device_schedules()
        all_devices = self._get_all_devices()
        
        # 3. 执行设备重新分配算法
        device_changes = []
        for rental in pending_rentals:
            change = self._find_optimal_device(rental, device_schedules, all_devices)
            device_changes.append(change)
            
            # 更新设备占用状态（用于后续订单计算）
            if change.status == "success" and change.new_device != change.original_device:
                if change.new_device not in device_schedules:
                    device_schedules[change.new_device] = []
                device_schedules[change.new_device].append({
                    'start_date': change.rental_time_start,
                    'end_date': change.rental_time_end,
                    'rental_id': change.rental_id
                })
        
        # 4. 生成统计摘要
        summary = self._generate_summary(device_changes)
        
        return DeviceAllocationPreview(
            summary=summary,
            device_changes=device_changes
        )
    
    def _get_pending_rentals(self) -> List[Rental]:
        """获取未发货的租赁记录"""
        return self.db.query(Rental).filter(
            and_(
                Rental.user_id == self.user_id,
                or_(
                    Rental.send_status == "未发货",
                    Rental.send_status.is_(None),
                    Rental.send_status == ""
                )
            )
        ).order_by(Rental.start_date, Rental.created_at).all()
    
    def _get_device_schedules(self) -> Dict[str, List[Dict]]:
        """获取所有设备的当前占用情况"""
        # 获取当前日期之后的所有已发货租赁记录（排除未发货的）
        today = date.today()
        future_rentals = self.db.query(Rental).filter(
            and_(
                Rental.user_id == self.user_id,
                Rental.end_date >= today,
                # 排除未发货订单：只考虑已发货、已收货、已归还的订单
                Rental.send_status.in_(["已发货", "已收货", "已归还"])
            )
        ).all()
        
        device_schedules = {}
        for rental in future_rentals:
            if rental.device_number:
                if rental.device_number not in device_schedules:
                    device_schedules[rental.device_number] = []
                device_schedules[rental.device_number].append({
                    'start_date': rental.start_date,
                    'end_date': rental.end_date,
                    'rental_id': rental.id
                })
        
        # 按开始时间排序
        for device_number in device_schedules:
            device_schedules[device_number].sort(key=lambda x: x['start_date'])
        
        return device_schedules
    
    def _get_all_devices(self) -> Dict[str, Machine]:
        """获取用户的所有设备"""
        devices = self.db.query(Machine).filter(Machine.user_id == self.user_id).all()
        return {device.device_number: device for device in devices}
    
    def _find_optimal_device(self, rental: Rental, device_schedules: Dict, all_devices: Dict) -> DeviceChange:
        """为订单找到最优设备分配"""
        original_device = rental.device_number
        rental_start = rental.start_date
        rental_end = rental.end_date
        
        # 如果没有指定设备编号，返回警告
        if not original_device:
            return DeviceChange(
                rental_id=rental.id,
                customer_name=rental.customer_name,
                rental_time_start=rental_start,
                rental_time_end=rental_end,
                original_device="未指定",
                new_device="未指定",
                change_reason="未指定设备编号，无法自动分配",
                status="warning",
                conflicts=["设备编号为空"]
            )
        
        # 检查原设备是否可用
        if self._is_device_available(original_device, rental_start, rental_end, device_schedules):
            return DeviceChange(
                rental_id=rental.id,
                customer_name=rental.customer_name,
                rental_time_start=rental_start,
                rental_time_end=rental_end,
                original_device=original_device,
                new_device=original_device,
                change_reason="原设备可用，无需更换",
                status="success"
            )
        
        # 寻找替代设备
        available_devices = self._find_available_devices(
            original_device, rental_start, rental_end, device_schedules, all_devices
        )
        
        if available_devices:
            best_device = self._select_best_device(available_devices, all_devices)
            # 生成更详细的变更原因说明
            original_device_info = all_devices.get(original_device)
            best_device_info = all_devices.get(best_device)
            
            if original_device_info and best_device_info:
                original_type = original_device_info.device_type or "通用设备"
                best_type = best_device_info.device_type or "通用设备"
                if original_type == best_type:
                    change_reason = f"原设备{original_device}在该时间段被占用，分配同类型设备{best_device}"
                else:
                    change_reason = f"原设备{original_device}({original_type})被占用，分配替代设备{best_device}({best_type})"
            else:
                change_reason = f"原设备{original_device}被占用，分配替代设备{best_device}"
            
            return DeviceChange(
                rental_id=rental.id,
                customer_name=rental.customer_name,
                rental_time_start=rental_start,
                rental_time_end=rental_end,
                original_device=original_device,
                new_device=best_device,
                change_reason=change_reason,
                status="success"
            )
        else:
            return DeviceChange(
                rental_id=rental.id,
                customer_name=rental.customer_name,
                rental_time_start=rental_start,
                rental_time_end=rental_end,
                original_device=original_device,
                new_device=original_device,
                change_reason="未找到可用的替代设备",
                status="conflict",
                conflicts=["无可用设备"]
            )
    
    def _is_device_available(self, device_number: str, start_date: date, end_date: date, schedules: Dict) -> bool:
        """检查设备在指定时间段是否可用（考虑维护间隔时间）"""
        device_schedule = schedules.get(device_number, [])
        
        for existing_rental in device_schedule:
            # 考虑维护间隔时间的时间重叠检查
            existing_start_with_buffer = existing_rental['start_date'] - timedelta(days=self.config.buffer_days)
            existing_end_with_buffer = existing_rental['end_date'] + timedelta(days=self.config.buffer_days)
            
            # 检查时间重叠（修复逻辑：两个时间段重叠当且仅当 start1 < end2 且 start2 < end1）
            if (start_date < existing_end_with_buffer and 
                existing_start_with_buffer < end_date):
                return False
        
        return True
    
    def _find_available_devices(self, original_device: str, start_date: date, end_date: date, 
                               device_schedules: Dict, all_devices: Dict) -> List[str]:
        """寻找可用的替代设备"""
        available_devices = []
        
        # 获取原设备类型
        original_device_info = all_devices.get(original_device)
        if not original_device_info:
            # 如果原设备不存在，返回所有可用设备
            for device_number, device_info in all_devices.items():
                if self._is_device_available(device_number, start_date, end_date, device_schedules):
                    available_devices.append(device_number)
        else:
            # 优先寻找同类型的可用设备
            original_device_type = original_device_info.device_type or "通用设备"
            same_type_devices = []
            other_available_devices = []
            
            for device_number, device_info in all_devices.items():
                if self._is_device_available(device_number, start_date, end_date, device_schedules):
                    device_type = device_info.device_type or "通用设备"
                    if device_type == original_device_type:
                        same_type_devices.append(device_number)
                    elif self.config.allow_device_upgrade:
                        # 如果允许设备升级，也可以考虑其他类型设备
                        other_available_devices.append(device_number)
            
            # 优先返回同类型设备，如果没有再考虑其他设备
            available_devices = same_type_devices + other_available_devices
        
        return available_devices
    
    def _select_best_device(self, available_devices: List[str], all_devices: Dict) -> str:
        """从可用设备中选择最佳设备"""
        if not available_devices:
            return ""
        
        # 如果配置为优先保持原设备，且原设备在可用列表中，优先选择原设备
        if self.config.prefer_same_device and len(available_devices) == 1:
            return available_devices[0]
        
        # 简单策略：优先选择设备编号较小的（保证一致性）
        # 可以扩展为更复杂的策略，如考虑设备使用频率、地理位置等
        return min(available_devices)
    
    def _generate_summary(self, device_changes: List[DeviceChange]) -> Dict[str, int]:
        """生成统计摘要"""
        total_orders = len(device_changes)
        success_count = sum(1 for change in device_changes if change.status == "success")
        conflict_count = sum(1 for change in device_changes if change.status == "conflict")
        warning_count = sum(1 for change in device_changes if change.status == "warning")
        optimized_count = sum(1 for change in device_changes 
                            if change.status == "success" and change.new_device != change.original_device)
        
        return {
            "total_orders": total_orders,
            "success_count": success_count,
            "conflict_count": conflict_count,
            "warning_count": warning_count,
            "optimized_count": optimized_count
        }

# 新的设备分配API接口
@router.post("/auto-device-allocation/preview", response_model=DeviceAllocationPreview)
def preview_auto_device_allocation(
    config: DeviceAllocationConfig = DeviceAllocationConfig(),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """生成自动设备分配预览"""
    allocator = AutoDeviceAllocator(db, current_user.id, config)
    return allocator.generate_allocation_preview()

@router.post("/auto-device-allocation/confirm")
def confirm_auto_device_allocation(
    confirm_data: DeviceAllocationConfirmData,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """确认并执行自动设备分配"""
    try:
        success_count = 0
        error_count = 0
        error_details = []
        
        for change in confirm_data.device_changes:
            if change.status == "success":
                # 更新租赁记录的设备分配
                rental = db.query(Rental).filter(
                    and_(
                        Rental.id == change.rental_id,
                        Rental.user_id == current_user.id
                    )
                ).first()
                
                if rental:
                    rental.device_number = change.new_device
                    
                    # 如果设置了自动更新发货状态
                    if confirm_data.auto_update_send_status and not rental.send_status:
                        rental.send_status = "未发货"
                    
                    success_count += 1
                else:
                    error_count += 1
                    error_details.append(f"租赁记录ID {change.rental_id} 不存在")
        
        db.commit()
        
        return {
            "message": "自动设备分配执行完成",
            "success_count": success_count,
            "error_count": error_count,
            "error_details": error_details
        }
    
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"自动设备分配执行失败: {str(e)}") 

@router.post("/{rental_id}/return")
def return_device(
    rental_id: int,
    data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """设备入库（更新租赁单状态为已归还）"""
    # 获取租赁单
    rental = db.query(Rental).filter(
        and_(
            Rental.id == rental_id,
            Rental.user_id == current_user.id
        )
    ).first()
    
    if not rental:
        raise HTTPException(status_code=404, detail="租赁单不存在")
    
    # 验证SN码是否匹配（如果提供了SN码）
    sn_code = data.get("sn_code")
    if sn_code:
        # 获取设备信息
        machine = db.query(Machine).filter(
            and_(
                Machine.device_number == rental.device_number,
                Machine.user_id == current_user.id
            )
        ).first()
        
        if machine and machine.sn_code != sn_code:
            raise HTTPException(status_code=400, detail="SN码与设备不匹配")
    
    # 更新租赁单状态为已归还
    rental.send_status = "已归还"
    db.commit()
    
    return {"message": "设备入库成功", "rental_id": rental_id}

@router.post("/{rental_id}/ship")
def ship_device(
    rental_id: int,
    data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """设备出库（更新租赁单状态为已发货）"""
    # 获取租赁单
    rental = db.query(Rental).filter(
        and_(
            Rental.id == rental_id,
            Rental.user_id == current_user.id
        )
    ).first()
    
    if not rental:
        raise HTTPException(status_code=404, detail="租赁单不存在")
    
    # 验证SN码是否匹配（如果提供了SN码）
    sn_code = data.get("sn_code")
    if sn_code:
        # 获取设备信息
        machine = db.query(Machine).filter(
            and_(
                Machine.device_number == rental.device_number,
                Machine.user_id == current_user.id
            )
        ).first()
        
        if machine and machine.sn_code != sn_code:
            raise HTTPException(status_code=400, detail="SN码与设备不匹配")
    
    # 更新快递单号（如果提供了）
    tracking_number = data.get("tracking_number")
    if tracking_number:
        rental.send_tracking = tracking_number
    
    # 更新租赁单状态为已发货
    rental.send_status = "已发货"
    db.commit()
    
    return {"message": "设备出库成功", "rental_id": rental_id} 