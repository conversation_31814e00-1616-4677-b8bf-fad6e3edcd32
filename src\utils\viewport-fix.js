// 修复iPhone等移动设备上的视口高度问题
// 主要解决100vh在移动端浏览器中的不准确问题

/**
 * 初始化视口高度修复
 * 设置CSS自定义属性--vh为实际视口高度的1%
 */
export function initViewportFix() {
  // 计算1vh的实际像素值
  const vh = window.innerHeight * 0.01
  // 设置CSS自定义属性
  document.documentElement.style.setProperty('--vh', `${vh}px`)
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    const vh = window.innerHeight * 0.01
    document.documentElement.style.setProperty('--vh', `${vh}px`)
  })
  
  // 监听方向变化
  window.addEventListener('orientationchange', () => {
    // 延迟执行，等待浏览器完成方向变化
    setTimeout(() => {
      const vh = window.innerHeight * 0.01
      document.documentElement.style.setProperty('--vh', `${vh}px`)
    }, 100)
  })
}

/**
 * 检测是否为iPhone设备
 */
export function isIPhone() {
  return /iPhone|iPod/.test(navigator.userAgent)
}

/**
 * 检测是否为iPhone X系列（有刘海屏）
 */
export function isIPhoneX() {
  if (!isIPhone()) return false
  
  // iPhone X系列的屏幕尺寸特征
  const { screen } = window
  const { width, height } = screen
  
  // iPhone X, XS, 11 Pro: 375x812
  // iPhone XR, 11: 414x896
  // iPhone XS Max, 11 Pro Max: 414x896
  // iPhone 12 mini: 375x812
  // iPhone 12, 12 Pro: 390x844
  // iPhone 12 Pro Max: 428x926
  // iPhone 13 mini: 375x812
  // iPhone 13, 13 Pro: 390x844
  // iPhone 13 Pro Max: 428x926
  // iPhone 14: 390x844
  // iPhone 14 Plus: 428x926
  // iPhone 14 Pro: 393x852
  // iPhone 14 Pro Max: 430x932
  
  return (
    (width === 375 && height === 812) ||
    (width === 414 && height === 896) ||
    (width === 390 && height === 844) ||
    (width === 428 && height === 926) ||
    (width === 393 && height === 852) ||
    (width === 430 && height === 932)
  )
}

/**
 * 获取安全区域插入值
 */
export function getSafeAreaInsets() {
  const style = getComputedStyle(document.documentElement)
  
  return {
    top: parseInt(style.getPropertyValue('env(safe-area-inset-top)')) || 0,
    right: parseInt(style.getPropertyValue('env(safe-area-inset-right)')) || 0,
    bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)')) || 0,
    left: parseInt(style.getPropertyValue('env(safe-area-inset-left)')) || 0
  }
}

/**
 * 为弹窗添加移动端适配类
 */
export function addMobileDialogClass() {
  const isMobile = window.innerWidth <= 768
  const isSmallMobile = window.innerWidth <= 480
  
  if (isMobile) {
    document.body.classList.add('mobile-viewport')
  } else {
    document.body.classList.remove('mobile-viewport')
  }
  
  if (isSmallMobile) {
    document.body.classList.add('small-mobile-viewport')
  } else {
    document.body.classList.remove('small-mobile-viewport')
  }
  
  if (isIPhone()) {
    document.body.classList.add('iphone-viewport')
  }
  
  if (isIPhoneX()) {
    document.body.classList.add('iphone-x-viewport')
  }
}

/**
 * 防止iOS Safari弹窗滚动时页面跟随滚动
 */
export function preventBodyScroll() {
  let scrollTop = 0
  
  return {
    lock() {
      scrollTop = document.documentElement.scrollTop || document.body.scrollTop
      document.body.style.position = 'fixed'
      document.body.style.top = `-${scrollTop}px`
      document.body.style.width = '100%'
    },
    unlock() {
      document.body.style.position = ''
      document.body.style.top = ''
      document.body.style.width = ''
      document.documentElement.scrollTop = scrollTop
      document.body.scrollTop = scrollTop
    }
  }
}

/**
 * 初始化所有移动端适配
 */
export function initMobileAdaptation() {
  // 初始化视口高度修复
  initViewportFix()
  
  // 添加移动端适配类
  addMobileDialogClass()
  
  // 监听窗口大小变化，更新适配类
  window.addEventListener('resize', addMobileDialogClass)
  window.addEventListener('orientationchange', () => {
    setTimeout(addMobileDialogClass, 100)
  })
}

// 自动初始化
if (typeof window !== 'undefined') {
  // DOM加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initMobileAdaptation)
  } else {
    initMobileAdaptation()
  }
} 