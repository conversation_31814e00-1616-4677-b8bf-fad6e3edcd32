export default {
  computed: {
    device() {
      return this.$store.state.app.device
    }
  },
  mounted() {
    // In order to fix the click on menu on the ios device will trigger the mouseleave bug
    // https://github.com/PanJiaChen/vue-element-admin/issues/1135
    this.fixBugIniOS()
    
    // 修复iOS上的点击延迟问题
    this.fixTouchDelayIniOS()
  },
  methods: {
    fixBugIniOS() {
      const $subMenu = this.$refs.subMenu
      if ($subMenu) {
        const handleMouseleave = $subMenu.handleMouseleave
        $subMenu.handleMouseleave = (e) => {
          if (this.device === 'mobile') {
            return
          }
          handleMouseleave(e)
        }
      }
    },
    
    fixTouchDelayIniOS() {
      // 只在移动设备上应用此修复
      if (this.device !== 'mobile') return
      
      // 获取子菜单元素
      const $subMenu = this.$refs.subMenu
      if (!$subMenu) return
      
      // 为菜单项添加touchstart事件处理
      const menuItems = $subMenu.$el.querySelectorAll('.el-submenu__title, .el-menu-item')
      
      menuItems.forEach(item => {
        item.addEventListener('touchstart', (e) => {
          // 阻止默认行为以防止双击缩放
          e.preventDefault()
          
          // 模拟点击事件
          item.click()
          
          // 阻止后续的click事件触发
          e.stopPropagation()
        }, false)
      })
    }
  }
}
