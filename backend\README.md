# 租赁管理系统后端API

基于FastAPI + SQLite的租赁管理系统后端API

## 功能特性

- 用户认证（注册、登录、JWT Token）
- 机器管理（增删改查）
- 租赁管理（租赁记录的增删改查）
- 自动创建数据库和表
- CORS支持前端跨域访问

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行应用

### 方法1：使用Python直接运行
```bash
python start.py
```

### 方法2：使用uvicorn命令行
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## API文档

启动应用后，访问以下地址查看API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## API端点

### 认证相关
- POST `/api/auth/register` - 用户注册
- POST `/api/auth/login` - 用户登录
- GET `/api/auth/me` - 获取当前用户信息

### 机器管理
- GET `/api/machines/` - 获取机器列表
- POST `/api/machines/` - 创建机器
- GET `/api/machines/{id}` - 获取机器详情
- PUT `/api/machines/{id}` - 更新机器信息
- DELETE `/api/machines/{id}` - 删除机器

### 租赁管理
- GET `/api/rentals/` - 获取租赁记录列表
- POST `/api/rentals/` - 创建租赁记录
- GET `/api/rentals/{id}` - 获取租赁记录详情
- PUT `/api/rentals/{id}` - 更新租赁记录
- DELETE `/api/rentals/{id}` - 删除租赁记录
- GET `/api/rentals/by-device/{device_number}` - 根据设备编号获取租赁记录

## 数据库

项目使用SQLite数据库，数据库文件为 `rental_system.db`，首次运行时自动创建。

## 配置

主要配置在 `config.py` 文件中：

- `SECRET_KEY`: JWT密钥
- `ACCESS_TOKEN_EXPIRE_MINUTES`: Token过期时间（分钟）
- `DATABASE_URL`: 数据库连接URL

## 默认账号

系统启动时会自动创建默认管理员账号：

- **用户名**: admin
- **密码**: Admin@2024#Secure!

⚠️ **安全提示**: 建议首次登录后立即修改默认密码！

⚠️ **重要提示**: 请在生产环境中及时修改默认密码！

## 注意事项

1. 首次运行会自动创建数据库表
2. 默认运行在8000端口
3. 开发模式下启用热重载
4. 启动时会自动创建默认管理员账号 