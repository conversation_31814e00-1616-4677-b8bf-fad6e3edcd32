from sqlalchemy import Column, Integer, String, DateTime, Date, Float, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database.database import Base

class Rental(Base):
    __tablename__ = "rentals"
    
    id = Column(Integer, primary_key=True, index=True)
    device_number = Column(String(100), nullable=True)
    xianyu_order = Column(String(100), nullable=True)
    send_tracking = Column(String(100), nullable=True)
    receive_tracking = Column(String(100), nullable=True)
    send_date = Column(DateTime, nullable=True)
    send_status = Column(String(50), nullable=True)
    customer_name = Column(String(100), nullable=True)
    address = Column(Text, nullable=True)
    start_date = Column(Date, nullable=False)
    end_date = Column(Date, nullable=False)
    rent_amount = Column(Float, nullable=False)
    accessories_note = Column(Text, nullable=False)
    note = Column(Text, nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联用户表
    owner = relationship("User", foreign_keys=[user_id]) 