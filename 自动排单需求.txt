# 自动排单需求规格书

## 一、功能概述
自动排单功能是租赁管理系统的核心功能之一，用于自动优化设备分配，通过重新分配空闲设备来提高设备利用率和操作效率。**客户的租赁时间保持不变，只调整设备分配。**

## 二、触发条件
### 2.1 状态判断
- **前置条件**：租赁记录的发货状态为"未发货"
- **触发时机**：用户点击"自动排单"按钮时
- **适用范围**：当前用户下所有状态为"未发货"的租赁记录

### 2.2 数据来源
- 从租赁管理列表页面获取未发货订单
- 系统后台数据库中 `send_status = "未发货"` 的租赁记录
- 关联用户ID确保数据隔离

## 三、自动排单核心逻辑

### 3.1 基础排单原则
```
核心原则：客户租赁时间不变，只调整设备分配

优化目标：
- 提高设备利用率
- 减少设备空闲时间
- 优化设备维护时间安排
- 避免设备使用冲突
```

### 3.2 排单算法流程
1. **获取待排单数据**
   - 筛选当前用户下发货状态为"未发货"的记录
   - 保持客户原有的租赁开始和结束时间不变
   - 按租赁时间段排序

2. **设备可用性分析**
   - 分析所有设备在各个时间段的占用情况
   - 识别空闲设备和时间段
   - 计算设备维护时间窗口

3. **设备重新分配逻辑**
   ```
   FOR 每个未发货订单:
     原始设备 = 订单当前分配的设备
     租赁时间段 = [客户开始时间, 客户结束时间]  // 不可变
     
     // 检查原设备是否可用
     IF 原设备在该时间段空闲:
       保持原分配
     ELSE:
       // 寻找替代设备
       FOR 每台同类型空闲设备:
         IF 设备在该时间段完全空闲:
           分配该设备
           BREAK
       
       IF 未找到合适设备:
         标记为冲突，需要手动处理
   ```

### 3.3 优化策略
1. **设备类型匹配**：确保替代设备功能完全相同
2. **空闲时间最大化**：优先选择能产生更长连续空闲时间的分配方案
3. **维护时间优化**：为设备预留维护保养时间
4. **地理位置考虑**：优先分配距离客户较近的设备（如适用）

## 四、排单预览功能

### 4.1 预览界面设计
- **布局**：类似现有排单管理页面（`src/views/rental/schedule.vue`）
- **展示内容**：
  - 订单信息（客户、时间保持不变）
  - 原设备编号 vs 新设备编号对比
  - 设备冲突标识和警告提示
  - 分配成功/失败状态标识

### 4.2 预览数据结构
```javascript
{
  preview_data: {
    summary: {
      total_orders: 15,        // 总待排单数
      success_count: 12,       // 成功分配数
      conflict_count: 3,       // 冲突订单数
      optimized_count: 8       // 优化调整数
    },
    device_changes: [
      {
        rental_id: 123,
        customer_name: "张三",
        rental_time: {           // 客户时间不变
          start_date: "2024-01-15",
          end_date: "2024-01-20"
        },
        device_change: {
          original_device: "DEV001",
          new_device: "DEV005",        // 新分配的设备
          change_reason: "DEV001在该时间段被其他订单占用"
        },
        status: "success",             // success/conflict/warning
        conflicts: []                  // 冲突信息
      }
    ]
  }
}
```

### 4.3 交互功能
- **预览确认**：显示所有设备分配变更明细
- **手动调整**：允许用户手动选择替代设备
- **冲突解决**：提供设备冲突解决建议
- **批量操作**：支持批量确认或取消变更

## 五、技术实现方案

### 5.1 前端实现
#### 5.1.1 在租赁管理页面添加自动排单按钮
```vue
<!-- src/views/rental/list.vue -->
<el-button 
  class="filter-item" 
  type="warning" 
  icon="el-icon-s-operation"
  @click="handleAutoDeviceAllocation"
  :disabled="!hasPendingOrders"
>
  自动分配设备
</el-button>
```

#### 5.1.2 设备分配预览对话框
- 复用 `schedule.vue` 的表格布局
- 添加设备对比显示功能（原设备 vs 新设备）
- 增加确认和取消操作

#### 5.1.3 API接口调用
```javascript
// src/api/rental.js
export function getAutoDeviceAllocationPreview() {
  return request({
    url: '/rentals/auto-device-allocation/preview',
    method: 'post'
  })
}

export function confirmAutoDeviceAllocation(allocation_data) {
  return request({
    url: '/rentals/auto-device-allocation/confirm',
    method: 'post',
    data: allocation_data
  })
}
```

### 5.2 后端实现
#### 5.2.1 新增API接口
```python
# backend/routers/rentals.py

@router.post("/auto-device-allocation/preview")
def preview_auto_device_allocation(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """生成自动设备分配预览"""
    # 实现自动设备分配逻辑
    # 返回分配预览数据

@router.post("/auto-device-allocation/confirm")
def confirm_auto_device_allocation(
    allocation_data: AllocationData,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """确认并执行自动设备分配"""
    # 批量更新租赁记录的设备分配
    # 返回执行结果
```

#### 5.2.2 数据模型扩展
```python
# backend/app/schemas.py
class DeviceAllocationConfig(BaseModel):
    prefer_same_device: bool = True    # 优先保持原设备
    consider_location: bool = False    # 是否考虑地理位置
    allow_device_upgrade: bool = True  # 是否允许设备升级

class DeviceChange(BaseModel):
    rental_id: int
    original_device: str
    new_device: str
    change_reason: str
    status: str                        # success/conflict/warning
```

### 5.3 核心算法实现
```python
class AutoDeviceAllocator:
    def __init__(self, db: Session, user_id: int):
        self.db = db
        self.user_id = user_id
    
    def generate_allocation(self) -> AllocationPreview:
        """生成设备分配预览"""
        # 1. 获取待分配订单（保持客户时间不变）
        pending_rentals = self.get_pending_rentals()
        
        # 2. 获取所有设备的占用情况
        all_devices = self.get_all_devices()
        device_schedules = self.get_device_schedules()
        
        # 3. 执行设备重新分配算法
        allocation_changes = []
        for rental in pending_rentals:
            new_allocation = self.find_optimal_device(rental, device_schedules)
            allocation_changes.append(new_allocation)
        
        return AllocationPreview(allocation_changes)
    
    def find_optimal_device(self, rental: Rental, device_schedules: Dict) -> DeviceChange:
        """为订单找到最优设备分配"""
        original_device = rental.device_number
        rental_start = rental.start_date
        rental_end = rental.end_date
        
        # 检查原设备是否可用
        if self.is_device_available(original_device, rental_start, rental_end, device_schedules):
            return DeviceChange(
                rental_id=rental.id,
                original_device=original_device,
                new_device=original_device,
                change_reason="原设备可用，无需更换",
                status="success"
            )
        
        # 寻找替代设备
        available_devices = self.find_available_devices(
            rental.device_type, 
            rental_start, 
            rental_end, 
            device_schedules
        )
        
        if available_devices:
            best_device = self.select_best_device(available_devices, rental)
            return DeviceChange(
                rental_id=rental.id,
                original_device=original_device,
                new_device=best_device,
                change_reason=f"原设备{original_device}被占用，分配到{best_device}",
                status="success"
            )
        else:
            return DeviceChange(
                rental_id=rental.id,
                original_device=original_device,
                new_device=original_device,
                change_reason="未找到可用的替代设备",
                status="conflict"
            )
    
    def is_device_available(self, device_number: str, start_date: date, end_date: date, schedules: Dict) -> bool:
        """检查设备在指定时间段是否可用"""
        device_schedule = schedules.get(device_number, [])
        
        for existing_rental in device_schedule:
            if (start_date < existing_rental.end_date and 
                end_date > existing_rental.start_date):
                return False
        
        return True
```

## 六、用户交互流程

### 6.1 操作步骤
1. **进入租赁管理页面**：用户查看当前租赁记录列表
2. **点击自动分配设备按钮**：系统检查是否有待发货订单
3. **生成分配预览**：系统计算并显示设备重新分配结果
4. **用户确认或调整**：
   - 查看设备分配变更明细
   - 手动调整有冲突的设备分配（可选）
   - 确认或取消整个分配操作
5. **执行分配**：系统批量更新租赁记录的设备分配
6. **结果反馈**：显示分配成功数量和失败原因

### 6.2 异常处理
- **无待发货订单**：提示用户当前无需重新分配
- **无可用设备**：提示用户手动选择设备或调整租赁计划
- **部分成功**：显示成功和失败的详细信息
- **系统错误**：记录错误日志并提示用户重试

## 七、配置参数

### 7.1 系统配置
- **设备类型映射**：定义哪些设备可以互相替代
- **维护时间预留**：设备归还后的维护时间
- **设备升级规则**：是否允许分配更高级的设备
- **地理位置权重**：距离因素在设备选择中的权重

### 7.2 用户个性化设置
- **保持原设备偏好**：优先保持原有设备分配
- **设备品牌偏好**：客户对特定品牌设备的偏好
- **通知设置**：设备变更通知方式

## 八、数据统计和报表

### 8.1 分配效果统计
- **设备利用率提升**：重新分配前后设备利用率对比
- **空闲时间优化**：设备空闲时间的减少情况
- **客户满意度**：设备变更对客户的影响评估

### 8.2 操作记录
- **分配历史**：保存每次自动分配的操作记录
- **设备变更日志**：详细记录每个订单的设备变更
- **性能监控**：分配算法的执行时间和资源消耗

## 九、后续优化方向

### 9.1 智能化增强
- **机器学习优化**：基于历史数据优化设备分配算法
- **需求预测**：根据设备使用模式预测最优分配
- **动态调整**：根据实时设备状态动态调整分配

### 9.2 集成扩展
- **消息通知**：自动发送设备变更通知给客户
- **维护计划集成**：与设备维护计划自动协调
- **库存管理**：实时同步设备状态和可用性

### 9.3 用户体验
- **可视化增强**：设备分配的直观图表显示
- **移动端适配**：支持移动设备上的设备分配操作
- **批量操作**：支持批量确认设备分配变更

---

## 总结
自动排单功能通过智能设备分配算法优化设备利用率，在保持客户租赁时间不变的前提下，重新分配空闲设备以减少冲突和提高效率。该功能基于现有系统架构实现，通过预览确认机制确保分配结果的准确性和用户的可控性。 