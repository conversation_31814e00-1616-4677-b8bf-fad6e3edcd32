import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register/index'),
    hidden: true
  },
  {
    path: '/auth-redirect',
    component: () => import('@/views/login/auth-redirect'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: { title: '仪表盘', icon: 'dashboard', affix: true, noCache: true }
      }
    ]
  }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  // 机器列表
  {
    path: '/machine',
    component: Layout,
    children: [
      {
        path: 'list',
        component: () => import('@/views/machine/list'),
        name: 'MachineList',
        meta: { title: '机器列表', icon: 'component' }
      }
    ]
  },
  // 租赁列表
  {
    path: '/rental',
    component: Layout,
    children: [
      {
        path: 'list',
        component: () => import('@/views/rental/list'),
        name: 'RentalList',
        meta: { title: '租赁列表', icon: 'shopping' }
      }
    ]
  },
  // 排单管理
  {
    path: '/schedule',
    component: Layout,
    children: [
      {
        path: 'index',
        component: () => import('@/views/rental/schedule'),
        name: 'RentalSchedule',
        meta: { title: '排单管理', icon: 'table' }
      }
    ]
  },
  // 快递时效
  {
    path: '/express-delivery',
    component: Layout,
    children: [
      {
        path: 'index',
        component: () => import('@/views/express/delivery'),
        name: 'ExpressDelivery',
        meta: { title: '快递时效', icon: 'shopping' }
      }
    ]
  },
  // 物流查询
  {
    path: '/logistics-query',
    component: Layout,
    children: [
      {
        path: 'index',
        component: () => import('@/views/express/query'),
        name: 'LogisticsQuery',
        meta: { title: '物流查询', icon: 'search' }
      }
    ]
  },
  // 物流截图
  {
    path: '/logistics-screenshot',
    component: Layout,
    children: [
      {
        path: 'index',
        component: () => import('@/views/logistics-screenshot/index'),
        name: 'LogisticsScreenshot',
        meta: { title: '物流截图', icon: 'clipboard' }
      }
    ]
  },
  // 租赁黑名单
  {
    path: '/blacklist',
    component: Layout,
    children: [
      {
        path: 'index',
        component: () => import('@/views/blacklist/index'),
        name: 'BlacklistIndex',
        meta: { title: '租赁黑名单', icon: 'peoples' }
      }
    ]
  },
  // 库存管理
  {
    path: '/inventory',
    component: Layout,
    children: [
      {
        path: 'index',
        component: () => import('@/views/inventory/index'),
        name: 'InventoryIndex',
        meta: { title: '库存管理', icon: 'component' }
      }
    ]
  },
  // 话术管理
  {
    path: '/scripts',
    component: Layout,
    children: [
      {
        path: 'index',
        component: () => import('@/views/scripts/index'),
        name: 'ScriptManagement',
        meta: { title: '话术管理', icon: 'edit' }
      }
    ]
  },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
