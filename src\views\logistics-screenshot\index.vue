<template>
  <div class="logistics-screenshot-container">
    <div class="app-container">
      <div class="screenshot-header">
        <h1>物流截图生成器</h1>
        <p>将上图、动态文字和下图拼接成一张完整的物流截图</p>
      </div>

      <el-row :gutter="20">
        <!-- 左侧配置区域 -->
        <el-col :span="12">
          <el-card class="config-card">
            <div slot="header">
              <span>配置区域</span>
            </div>
            
            <!-- 中间文字配置 -->
            <div class="text-section">
              <h4>中间文字内容</h4>
              <el-form ref="textForm" :model="textConfig" label-width="80px" size="small">
                <el-form-item label="文字内容">
                  <el-input
                    v-model="textConfig.content"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入要显示的文字内容"
                  />
                </el-form-item>
              </el-form>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <el-button type="primary" @click="generateScreenshot" :loading="generating">
                <i class="el-icon-picture"></i>
                生成截图
              </el-button>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧预览区域 -->
        <el-col :span="12">
          <el-card class="preview-card">
            <div slot="header">
              <span>预览区域</span>
              <el-button
                v-if="finalImage"
                style="float: right; padding: 3px 0"
                type="text"
                @click="downloadImage"
              >
                下载图片
              </el-button>
            </div>
            
            <div class="preview-container">
              <div v-if="!finalImage" class="empty-preview">
                <i class="el-icon-picture-outline"></i>
                <p>点击"生成截图"按钮预览效果</p>
              </div>
              <div v-else class="final-preview">
                <img :src="finalImage" alt="最终截图" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 隐藏的canvas用于图片处理 -->
      <canvas ref="canvas" style="display: none;"></canvas>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LogisticsScreenshot',
  data() {
    return {
      finalImage: null,
      generating: false,
      textConfig: {
        content: '请选择退货方式，退回商品\n\n请在6天23小时内填写快递单号',
        fontSize: 50,
        color: '#333333',
        backgroundColor: '#F1EDED',
        textAlign: 'center',
        lineHeight: 1.8
      }
    }
  },
  methods: {
    
    async generateScreenshot() {
      if (!this.textConfig.content.trim()) {
        this.$message.error('请输入文字内容')
        return
      }
      
      this.generating = true
      
      try {
        await this.createCombinedImage()
        this.$message.success('截图生成成功')
      } catch (error) {
        console.error('生成截图失败:', error)
        this.$message.error('生成截图失败，请重试')
      } finally {
        this.generating = false
      }
    },
    
    async createCombinedImage() {
      const canvas = this.$refs.canvas
      const ctx = canvas.getContext('2d')
      
      // 加载固定的图片文件
      const topImg = await this.loadImage('/上.jpg')
      const bottomImg = await this.loadImage('/下.jpg')
      
      // 计算画布尺寸
      const maxWidth = Math.max(topImg.width, bottomImg.width)
      const textHeight = this.calculateTextHeight(ctx, this.textConfig.content, maxWidth)
      const totalHeight = topImg.height + textHeight + bottomImg.height
      
      canvas.width = maxWidth
      canvas.height = totalHeight
      
      // 绘制上图
      ctx.drawImage(topImg, (maxWidth - topImg.width) / 2, 0, topImg.width, topImg.height)
      
      // 绘制文字区域
      const textY = topImg.height
      this.drawTextArea(ctx, maxWidth, textHeight, textY)
      
      // 绘制下图
      const bottomY = topImg.height + textHeight
      ctx.drawImage(bottomImg, (maxWidth - bottomImg.width) / 2, bottomY, bottomImg.width, bottomImg.height)
      
      // 创建新的画布用于裁剪（去掉上下各20像素）
      const croppedCanvas = document.createElement('canvas')
      const croppedCtx = croppedCanvas.getContext('2d')
      
      // 设置裁剪后的画布尺寸
      const cropTop = 0
      const cropBottom = 0
      const croppedHeight = Math.max(0, totalHeight - cropTop - cropBottom)
      
      croppedCanvas.width = maxWidth
      croppedCanvas.height = croppedHeight
      
      // 如果裁剪后高度大于0，则进行裁剪
      if (croppedHeight > 0) {
        croppedCtx.drawImage(
          canvas,
          0, cropTop, maxWidth, croppedHeight,  // 源图片的裁剪区域
          0, 0, maxWidth, croppedHeight         // 目标画布的绘制区域
        )
        
        // 使用裁剪后的图片
        this.finalImage = croppedCanvas.toDataURL('image/png')
      } else {
        // 如果图片太小无法裁剪，使用原图
        this.finalImage = canvas.toDataURL('image/png')
      }
    },
    
    loadImage(src) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        img.onload = () => resolve(img)
        img.onerror = reject
        img.src = src
      })
    },
    
    calculateTextHeight(ctx, text, maxWidth) {
      const lines = text.split('\n')
      const lineHeight = this.textConfig.fontSize * this.textConfig.lineHeight
      const padding = 40 // 上下内边距
      return lines.length * lineHeight + padding
    },
    
    drawTextArea(ctx, width, height, y) {
      // 绘制背景
      ctx.fillStyle = this.textConfig.backgroundColor
      ctx.fillRect(0, y, width, height)
      
      // 设置文字样式
      ctx.fillStyle = this.textConfig.color
      ctx.font = `${this.textConfig.fontSize}px Arial, sans-serif`
      ctx.textAlign = this.textConfig.textAlign
      
      // 计算文字位置
      const lines = this.textConfig.content.split('\n')
      const lineHeight = this.textConfig.fontSize * this.textConfig.lineHeight
      const startY = y + 20 + this.textConfig.fontSize // 上内边距 + 字体高度
      
      let textX
      switch (this.textConfig.textAlign) {
        case 'left':
          textX = 20
          break
        case 'right':
          textX = width - 20
          break
        default: // center
          textX = width / 2
      }
      
      // 绘制每行文字
      lines.forEach((line, index) => {
        const textY = startY + index * lineHeight
        ctx.fillText(line, textX, textY)
      })
    },
    
    downloadImage() {
      if (!this.finalImage) return
      
      const link = document.createElement('a')
      link.download = `物流截图_${new Date().getTime()}.png`
      link.href = this.finalImage
      link.click()
    },
    
    loadTemplate(type) {
      if (type === 'refund') {
        this.textConfig = {
          content: '请选择退货方式，退回商品\n\n请在6天23小时内填写快递单号\n\n商家地址：牛雅楠17733882173\n河北省石家庄市新乐市邯郸镇新乐市\n邯郸镇东岳村 郭玉超市',
          fontSize: 14,
          color: '#333333',
          backgroundColor: '#ffffff',
          textAlign: 'left',
          lineHeight: 1.8
        }
      } else if (type === 'delivery') {
        this.textConfig = {
          content: '配送信息\n\n预计送达时间：2-3个工作日\n配送费用：免费配送\n\n请保持手机畅通，快递员会提前联系您',
          fontSize: 16,
          color: '#333333',
          backgroundColor: '#ffffff',
          textAlign: 'center',
          lineHeight: 1.6
        }
      }
    },
    
    resetAll() {
      this.finalImage = null
      this.textConfig = {
        content: '请选择退货方式，退回商品\n\n请在6天23小时内填写快递单号',
        fontSize: 50,
        color: '#333333',
        backgroundColor: '#F1EDED',
        textAlign: 'center',
        lineHeight: 1.8
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .logistics-screenshot-container {
    min-height: 100vh;
    
    .app-container {
      padding: 20px;
      min-height: calc(100vh - 40px);
    }
  
  .screenshot-header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      color: #303133;
      margin-bottom: 10px;
    }
    
    p {
      color: #606266;
      font-size: 14px;
    }
  }
  
  .config-card, .preview-card {
    min-height: 600px;
    
    .el-card__body {
      overflow-y: auto;
    }
  }
  
  .text-section {
    margin-bottom: 20px;

    h4 {
      color: #303133;
      margin-bottom: 10px;
      font-size: 14px;
    }
  }
  
  .action-buttons {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
    
    .el-button {
      margin: 0 10px;
    }
  }
  
  .preview-container {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .empty-preview {
      text-align: center;
      color: #909399;
      
      i {
        font-size: 48px;
        margin-bottom: 15px;
        display: block;
      }
    }
    
    .final-preview {
      text-align: center;
      
      img {
        max-width: 100%;
        max-height: 500px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style> 