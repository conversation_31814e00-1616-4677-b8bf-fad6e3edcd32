<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.search"
        placeholder="搜索设备编号或SN码"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">
        添加机器
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      @sort-change="handleSortChange"
    >
      <el-table-column align="center" label="ID" width="95">
        <template slot-scope="scope">
          {{ scope.row.id }}
        </template>
      </el-table-column>
      <el-table-column label="设备编号" width="110">
        <template slot-scope="scope">
          {{ scope.row.device_number }}
        </template>
      </el-table-column>
      <el-table-column label="SN码" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.sn_code }}</span>
        </template>
      </el-table-column>
      <el-table-column label="购买时间" width="110" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.purchase_date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="随心换到期日期" width="150" align="center" sortable="custom" prop="warranty_expire_date">
        <template slot-scope="scope">
          <span>{{ scope.row.warranty_expire_date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="230" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <!-- 添加/编辑对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :width="dialogWidth" :class="{'mobile-dialog': isMobile}">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" :label-width="isMobile ? '100px' : '120px'" :style="isMobile ? '' : 'width: 400px; margin-left:50px;'">
        <el-form-item label="设备编号" prop="device_number">
          <el-input v-model="temp.device_number" />
        </el-form-item>
        <el-form-item label="SN码" prop="sn_code">
          <el-input v-model="temp.sn_code" />
        </el-form-item>
        <el-form-item label="购买时间" prop="purchase_date">
          <el-date-picker v-model="temp.purchase_date" type="date" placeholder="选择购买时间" value-format="yyyy-MM-dd" />
        </el-form-item>
        <el-form-item label="随心换到期日期" prop="warranty_expire_date">
          <el-date-picker v-model="temp.warranty_expire_date" type="date" placeholder="选择到期日期" value-format="yyyy-MM-dd" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMachines, createMachine, updateMachine, deleteMachine } from '@/api/machine'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'MachineList',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 50,
        search: '',
        sort: ''
      },
      windowWidth: window.innerWidth,
      temp: {
        id: undefined,
        device_number: '',
        sn_code: '',
        purchase_date: '',
        warranty_expire_date: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑机器',
        create: '添加机器'
      },
      rules: {
        device_number: [{ required: true, message: '设备编号是必填项', trigger: 'blur' }],
        sn_code: [{ required: true, message: 'SN码是必填项', trigger: 'blur' }],
        purchase_date: [{ required: true, message: '购买时间是必填项', trigger: 'change' }],
        warranty_expire_date: [{ required: true, message: '随心换到期日期是必填项', trigger: 'change' }]
      }
    }
  },
  computed: {
    isMobile() {
      return this.windowWidth <= 768
    },
    dialogWidth() {
      if (this.windowWidth <= 480) {
        return '98%'
      } else if (this.windowWidth <= 768) {
        return '95%'
      } else {
        return '50%'
      }
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.handleResize()
    window.addEventListener('resize', this.handleResize)
  },
  // 页面被keep-alive激活时执行，确保每次进入页面都获取最新数据
  activated() {
    this.getList()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    parseTime,
    getList() {
      this.listLoading = true
      const params = {
        skip: (this.listQuery.page - 1) * this.listQuery.limit,
        limit: this.listQuery.limit
      }
      
      // 添加排序参数
      if (this.listQuery.sort) {
        params.sort = this.listQuery.sort
        console.log('添加排序参数到API请求:', params.sort)
      }
      
      // 添加搜索参数
      if (this.listQuery.search) {
        params.search = this.listQuery.search
      }
      
      getMachines(params).then(response => {
        console.log('机器列表API响应:', response)
        // 适应新的API响应格式
        if (response && response.items) {
          this.list = response.items
          this.total = response.total
        } else {
          // 兼容旧格式
          this.list = Array.isArray(response) ? response : []
          this.total = this.list.length
        }
        this.listLoading = false
      }).catch(error => {
        console.error('获取机器列表失败:', error)
        this.list = []
        this.total = 0
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleSortChange({ prop, order }) {
      console.log('排序变更:', { prop, order })
      
      // 映射前端属性名到后端字段名
      const fieldMapping = {
        'warranty_expire_date': 'warranty_expire_date'
      }
      
      if (!order) {
        // 取消排序
        this.listQuery.sort = ''
      } else {
        const backendField = fieldMapping[prop]
        if (backendField) {
          this.listQuery.sort = order === 'ascending' ? backendField : `-${backendField}`
        }
      }
      
      console.log('排序参数:', this.listQuery.sort)
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        device_number: '',
        sn_code: '',
        purchase_date: '',
        warranty_expire_date: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createMachine(this.temp).then(() => {
            this.list.unshift(this.temp)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          updateMachine(tempData.id, tempData).then(() => {
            const index = this.list.findIndex(v => v.id === this.temp.id)
            this.list.splice(index, 1, this.temp)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确定要删除这台机器吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteMachine(row.id).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          const index = this.list.findIndex(v => v.id === row.id)
          this.list.splice(index, 1)
        })
      })
    },
    handleResize() {
      this.windowWidth = window.innerWidth
    }
  }
}
</script> 