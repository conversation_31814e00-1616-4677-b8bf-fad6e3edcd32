<template>
  <div class="dashboard-editor-container">
    <!-- 统计面板 -->
    <panel-group @handleSetLineChartData="handleSetLineChartData" />

    <!-- 订单列表区域 -->
    <el-row :gutter="20" style="margin-bottom:32px;">
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <today-ship-orders ref="todayShipOrders" />
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <return-reminder-orders ref="returnReminderOrders" />
      </el-col>
    </el-row>

    <!-- 趋势图表 - 暂时隐藏 -->
    <!-- <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:32px;">
      <line-chart :chart-data="lineChartData" />
    </el-row> -->
  </div>
</template>

<script>
import PanelGroup from './components/PanelGroup'
import LineChart from './components/LineChart'
import TodayShipOrders from './components/TodayShipOrders'
import ReturnReminderOrders from './components/ReturnReminderOrders'
import { getTrendData, getDashboardStats } from '@/api/dashboard'

export default {
  name: '管理员仪表盘',
  components: {
    PanelGroup,
    LineChart,
    TodayShipOrders,
    ReturnReminderOrders
  },
  data() {
    return {
      lineChartData: {
        expectedData: [],
        actualData: []
      },
      currentDataType: 'machines',
      loading: false,
      stats: {
        total_machines: 0
      },
      lastUpdateTime: ''
    }
  },
  mounted() {
    this.initDashboard()
    this.updateTime()
    // 每5分钟自动刷新数据
    this.timer = setInterval(() => {
      this.fetchTrendData(this.currentDataType)
      this.updateTime()
      // 通知子组件刷新数据
      this.refreshOrderLists()
    }, 5 * 60 * 1000)
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  activated() {
    // 每次激活组件时重新加载数据
    this.initDashboard()
    this.updateTime()
    this.refreshOrderLists()
  },
  methods: {
    async initDashboard() {
      await this.fetchTrendData()
    },
    async handleSetLineChartData(type) {
      this.currentDataType = type
      await this.fetchTrendData(type)
    },
    async fetchTrendData(dataType = 'machines') {
      this.loading = true
      try {
        const response = await getTrendData(7)
        const trendData = response
        
        if (trendData && trendData[dataType]) {
          this.lineChartData = trendData[dataType]
        }
        
      } catch (error) {
        console.error('获取趋势数据失败:', error)
        this.$message.error('获取趋势数据失败')
        // 设置默认数据
        this.lineChartData = {
          expectedData: [0, 0, 0, 0, 0, 0, 0],
          actualData: [0, 0, 0, 0, 0, 0, 0]
        }
      } finally {
        this.loading = false
      }
    },
    updateTime() {
      this.lastUpdateTime = new Date().toLocaleString('zh-CN')
    },
    refreshOrderLists() {
      // 触发子组件的数据刷新
      this.$nextTick(() => {
        // 通过 $refs 调用子组件的刷新方法
        if (this.$refs.todayShipOrders) {
          this.$refs.todayShipOrders.fetchOrders()
        }
        if (this.$refs.returnReminderOrders) {
          this.$refs.returnReminderOrders.fetchOrders()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;
}

// 移动端适配
@media (max-width: 768px) {
  .dashboard-editor-container {
    padding: 15px 10px;
  }
}

// 小屏幕手机适配
@media (max-width: 480px) {
  .dashboard-editor-container {
    padding: 10px 5px;
  }
}
</style>
