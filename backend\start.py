import uvicorn
from main import app
from database.database import engine, SessionLocal
from database.init_data import init_database_data
from models import user, machine, rental  # 导入所有模型
from database.database import Base
import os

def recreate_database():
    """重新创建数据库表结构"""
    print("🔄 重新创建数据库表结构...")
    
    # 删除所有表
    Base.metadata.drop_all(bind=engine)
    
    # 重新创建所有表
    Base.metadata.create_all(bind=engine)
    
    print("✅ 数据库表结构创建完成!")

def init_app():
    """初始化应用"""
    print("🚀 开始初始化应用...")
    db_path = os.path.join(os.path.dirname(__file__), 'rental_system.db')
    if not os.path.exists(db_path):
        # 只有数据库文件不存在时才初始化
        recreate_database()
        db = SessionLocal()
        try:
            init_database_data(db)
        finally:
            db.close()
        print("✅ 应用初始化完成!")
    else:
        print("✅ 数据库已存在，跳过初始化!")

if __name__ == "__main__":
    # 初始化应用
    init_app()
    
    # 启动服务器
    print("🌐 启动服务器...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=["./"]
    ) 