<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.search"
        placeholder="搜索设备编号或客户姓名"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.status"
        placeholder="状态筛选"
        clearable
        style="width: 140px"
        class="filter-item"
        @change="handleFilter"
      >
        <el-option label="全部状态" value="" />
        <el-option label="未发货" value="未发货" />
        <el-option label="已发货" value="已发货" />
        <el-option label="已收货" value="已收货" />
      </el-select>
      <el-checkbox v-model="listQuery.showReturned" class="filter-item" style="margin-right: 15px;" @change="handleFilter">显示已归还</el-checkbox>
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">
        搜索
      </el-button>
      <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">
        添加租赁记录
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="Loading"
      border
      fit
      highlight-current-row
      @sort-change="handleSortChange"
    >
      <el-table-column align="center" label="ID" width="80">
        <template slot-scope="scope">
          {{ scope.row.id }}
        </template>
      </el-table-column>
      <el-table-column label="设备编号" width="120">
        <template slot-scope="scope">
          {{ scope.row.device_number || '未指定' }}
        </template>
      </el-table-column>
      <el-table-column label="客户姓名" width="100">
        <template slot-scope="scope">
          {{ scope.row.customer_name || '未填写' }}
        </template>
      </el-table-column>
      <el-table-column label="闲鱼单号" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.xianyu_order || '未填写' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发货时间" width="110" align="center" sortable prop="send_date">
        <template slot-scope="scope">
          <div :class="{ 'highlight-cell': isHighlightedSendDate(scope.row) }">
            {{ scope.row.send_date ? formatDate(scope.row.send_date) : '未发货' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" width="110" align="center" sortable prop="start_date">
        <template slot-scope="scope">
          <span>{{ scope.row.start_date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" width="110" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.end_date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="租金" width="80" align="center">
        <template slot-scope="scope">
          <span>¥{{ scope.row.rent_amount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发货状态" width="120" align="center" sortable prop="send_status">
        <template slot-scope="scope">
          <el-tag :type="statusTagType(scope.row.send_status)">
            {{ scope.row.send_status || '未发货' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="230" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button type="primary" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button size="mini" type="info" @click="handleView(row)">
            详情
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" :page-sizes="[20, 50, 100, 200]" @pagination="getList" />

    <!-- 添加/编辑对话框 -->
    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      :width="dialogWidth"
      :class="{'mobile-dialog': isMobile}"
      @close="handleDialogClose"
    >
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="left" :label-width="isMobile ? '100px' : '120px'">
        <el-row :gutter="isMobile ? 0 : 20">
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="设备编号" prop="device_number">
              <el-select 
                v-model="temp.device_number" 
                placeholder="请选择设备编号" 
                filterable 
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="machine in machineOptions"
                  :key="machine.id"
                  :label="machine.device_number"
                  :value="machine.device_number"
                >
                  <span style="float: left">{{ machine.device_number }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ machine.sn_code }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="闲鱼单号">
              <el-input v-model="temp.xianyu_order" placeholder="可选填" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="isMobile ? 0 : 20">
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="寄出快递单号">
              <el-input v-model="temp.send_tracking" placeholder="可选填" />
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="收货快递单号">
              <el-input v-model="temp.receive_tracking" placeholder="可选填" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="isMobile ? 0 : 20">
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="发货时间" prop="send_date">
              <el-date-picker v-model="temp.send_date" type="date" placeholder="选择发货时间" value-format="yyyy-MM-dd" />
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="发货状态">
              <el-select v-model="temp.send_status" placeholder="选择发货状态">
                <el-option label="未发货" value="未发货" />
                <el-option label="已发货" value="已发货" />
                <el-option label="已收货" value="已收货" />
                <el-option label="已归还" value="已归还" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="isMobile ? 0 : 20">
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="租客真实姓名">
              <el-input v-model="temp.customer_name" placeholder="可选填" />
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="租金" prop="rent_amount">
              <el-input-number v-model="temp.rent_amount" :precision="2" :step="0.01" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="isMobile ? 0 : 20">
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="开始时间" prop="start_date">
              <el-date-picker v-model="temp.start_date" type="date" placeholder="选择开始时间" value-format="yyyy-MM-dd" />
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="结束时间" prop="end_date">
              <el-date-picker v-model="temp.end_date" type="date" placeholder="选择结束时间" value-format="yyyy-MM-dd" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="地址">
          <el-input v-model="temp.address" type="textarea" :rows="2" placeholder="客户地址" />
        </el-form-item>
        <el-form-item label="配件备注" prop="accessories_note">
          <el-input v-model="temp.accessories_note" type="textarea" :rows="3" placeholder="配件备注（必填）" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="temp.note" type="textarea" :rows="3" placeholder="其他备注信息" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确认
        </el-button>
      </div>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog title="租赁详情" :visible.sync="detailDialogVisible" :width="dialogWidth" :class="{'mobile-dialog': isMobile}" @close="handleDetailDialogClose">
      <div style="padding: 20px;">
        <table class="detail-table">
          <tr>
            <td class="label">记录ID：</td>
            <td class="value">{{ viewData.id || '-' }}</td>
            <td class="label">设备编号：</td>
            <td class="value">{{ viewData.device_number || '-' }}</td>
          </tr>
          <tr>
            <td class="label">客户姓名：</td>
            <td class="value">{{ viewData.customer_name || '-' }}</td>
            <td class="label">租金：</td>
            <td class="value">¥{{ viewData.rent_amount || 0 }}</td>
          </tr>
          <tr>
            <td class="label">开始时间：</td>
            <td class="value">{{ viewData.start_date || '-' }}</td>
            <td class="label">结束时间：</td>
            <td class="value">{{ viewData.end_date || '-' }}</td>
          </tr>
          <tr>
            <td class="label">发货状态：</td>
            <td class="value">
              <el-tag :type="statusTagType(viewData.send_status)">{{ viewData.send_status || '未发货' }}</el-tag>
            </td>
            <td class="label">发货时间：</td>
            <td class="value">{{ viewData.send_date ? formatDate(viewData.send_date) : '-' }}</td>
          </tr>
          <tr>
            <td class="label">闲鱼单号：</td>
            <td class="value">{{ viewData.xianyu_order || '-' }}</td>
            <td class="label"></td>
            <td class="value"></td>
          </tr>
          <tr>
            <td class="label">寄出快递单号：</td>
            <td class="value">
              <span>{{ viewData.send_tracking || '-' }}</span>
              <el-button 
                v-if="viewData.send_tracking" 
                size="mini" 
                type="primary" 
                @click="queryLogistics(viewData.send_tracking)"
                style="margin-left: 10px;"
              >
                查询物流
              </el-button>
            </td>
            <td class="label">收货快递单号：</td>
            <td class="value">
              <span>{{ viewData.receive_tracking || '-' }}</span>
              <el-button 
                v-if="viewData.receive_tracking" 
                size="mini" 
                type="primary" 
                @click="queryLogistics(viewData.receive_tracking)"
                style="margin-left: 10px;"
              >
                查询物流
              </el-button>
            </td>
          </tr>
          <tr>
            <td class="label">地址：</td>
            <td class="value" colspan="3">{{ viewData.address || '-' }}</td>
          </tr>
          <tr>
            <td class="label">配件备注：</td>
            <td class="value" colspan="3">{{ viewData.accessories_note || '标准配件' }}</td>
          </tr>
          <tr>
            <td class="label">备注：</td>
            <td class="value" colspan="3">{{ viewData.note || '-' }}</td>
          </tr>
          <tr>
            <td class="label">创建时间：</td>
            <td class="value" colspan="3">{{ formatDateTime(viewData.created_at) }}</td>
          </tr>
        </table>
      </div>
    </el-dialog>

    <!-- 自动设备分配预览对话框 -->
    <el-dialog 
      title="智能设备分配预览" 
      :visible.sync="deviceAllocationDialogVisible" 
      :width="isMobile ? '98%' : '80%'"
      :class="{'mobile-dialog': isMobile}"
    >
      <div v-if="allocationPreview">
        <!-- 统计摘要 -->
        <div class="allocation-summary">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-number">{{ allocationPreview.summary.total_orders }}</div>
                <div class="summary-label">总订单数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item success">
                <div class="summary-number">{{ allocationPreview.summary.success_count }}</div>
                <div class="summary-label">成功分配</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item warning">
                <div class="summary-number">{{ allocationPreview.summary.optimized_count }}</div>
                <div class="summary-label">设备调整</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item danger">
                <div class="summary-number">{{ allocationPreview.summary.conflict_count + (allocationPreview.summary.warning_count || 0) }}</div>
                <div class="summary-label">冲突/警告</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 设备分配变更明细 -->
        <div class="device-changes">
          <h4>设备分配变更明细</h4>
          <el-table :data="allocationPreview.device_changes" stripe border>
            <el-table-column label="状态" width="80" align="center">
              <template slot-scope="scope">
                <el-tag :type="getDeviceChangeStatusType(scope.row.status)" size="small">
                  {{ getDeviceChangeStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="客户姓名" width="100">
              <template slot-scope="scope">
                {{ scope.row.customer_name || '未填写' }}
              </template>
            </el-table-column>
            <el-table-column label="租赁时间" width="220">
              <template slot-scope="scope">
                <span>{{ scope.row.rental_time_start }} 至 {{ scope.row.rental_time_end }}</span>
              </template>
            </el-table-column>
            <el-table-column label="原设备" prop="original_device" width="100" />
            <el-table-column label="新设备" width="100">
              <template slot-scope="scope">
                <span :class="{ 'text-changed': scope.row.new_device !== scope.row.original_device }">
                  {{ scope.row.new_device }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="变更原因" prop="change_reason" min-width="200" />
          </el-table>
        </div>

        <!-- 配置选项 -->
        <div class="allocation-config" style="margin-top: 20px;">
          <h4>分配配置</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item>
                <span slot="label">
                  维护间隔天数
                  <el-tooltip content="设备归还后到下次出租的维护时间间隔，用于避免设备冲突" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>:
                </span>
                <el-input-number 
                  v-model="allocationConfig.buffer_days" 
                  :min="0" 
                  :max="7" 
                  size="small"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <span slot="label">
                  优先保持原设备
                  <el-tooltip content="尽量保持原有设备分配，只在必要时才更换设备" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>:
                </span>
                <el-switch 
                  v-model="allocationConfig.prefer_same_device"
                  active-text="是"
                  inactive-text="否"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item>
                <span slot="label">
                  允许设备升级
                  <el-tooltip content="当同类型设备不可用时，是否允许分配其他类型的设备" placement="top">
                    <i class="el-icon-question"></i>
                  </el-tooltip>:
                </span>
                <el-switch 
                  v-model="allocationConfig.allow_device_upgrade"
                  active-text="是"
                  inactive-text="否"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="deviceAllocationDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmDeviceAllocation"
          :loading="deviceAllocationLoading"
          :disabled="!allocationPreview || allocationPreview.summary.success_count === 0"
        >
          确认分配 ({{ allocationPreview ? allocationPreview.summary.success_count : 0 }} 个订单)
        </el-button>
      </div>
    </el-dialog>

    <!-- 保留原有自动排单对话框以兼容 -->
    <el-dialog 
      title="自动排单预览" 
      :visible.sync="autoScheduleDialogVisible" 
      :width="isMobile ? '98%' : '80%'"
      :class="{'mobile-dialog': isMobile}"
    >
      <div v-if="schedulePreview">
        <!-- 统计摘要 -->
        <div class="schedule-summary">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="summary-item">
                <div class="summary-number">{{ schedulePreview.summary.total_orders }}</div>
                <div class="summary-label">总订单数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item success">
                <div class="summary-number">{{ schedulePreview.summary.success_count }}</div>
                <div class="summary-label">成功排单</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item warning">
                <div class="summary-number">{{ schedulePreview.summary.optimized_count }}</div>
                <div class="summary-label">时间调整</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="summary-item danger">
                <div class="summary-number">{{ schedulePreview.summary.conflict_count + (schedulePreview.summary.warning_count || 0) }}</div>
                <div class="summary-label">冲突/警告</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 排单变更明细 -->
        <div class="schedule-changes">
          <h4>排单变更明细</h4>
          <el-table :data="schedulePreview.schedule_changes" stripe border>
            <el-table-column label="状态" width="80" align="center">
              <template slot-scope="scope">
                <el-tag :type="getScheduleChangeStatusType(scope.row.status)" size="small">
                  {{ getScheduleChangeStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="设备编号" prop="device_number" width="100" />
            <el-table-column label="客户姓名" width="100">
              <template slot-scope="scope">
                {{ scope.row.customer_name || '未填写' }}
              </template>
            </el-table-column>
            <el-table-column label="原开始时间" prop="original_start_date" width="110" />
            <el-table-column label="原结束时间" prop="original_end_date" width="110" />
            <el-table-column label="新开始时间" width="110">
              <template slot-scope="scope">
                <span :class="{ 'text-changed': scope.row.new_start_date !== scope.row.original_start_date }">
                  {{ scope.row.new_start_date }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="新结束时间" width="110">
              <template slot-scope="scope">
                <span :class="{ 'text-changed': scope.row.new_end_date !== scope.row.original_end_date }">
                  {{ scope.row.new_end_date }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="调整原因" prop="reason" min-width="200" />
          </el-table>
        </div>

        <!-- 配置选项 -->
        <div class="schedule-config" style="margin-top: 20px;">
          <h4>排单配置</h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="间隔天数:">
                <el-input-number v-model="scheduleConfig.buffer_days" :min="0" :max="7" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="最大调整天数:">
                <el-input-number v-model="scheduleConfig.max_reschedule_days" :min="1" :max="365" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="autoScheduleDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="confirmSchedule"
          :loading="autoScheduleLoading"
          :disabled="!schedulePreview || schedulePreview.summary.success_count === 0"
        >
          确认排单 ({{ schedulePreview ? schedulePreview.summary.success_count : 0 }} 个订单)
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRentals, createRental, updateRental, deleteRental, getAutoDeviceAllocationPreview, confirmAutoDeviceAllocation, getAutoSchedulePreview, confirmAutoSchedule } from '@/api/rental'
import { getMachines } from '@/api/machine'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination'

export default {
  name: 'RentalList',
  components: { Pagination },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 50,
        search: '',
        sort: '',
        status: '',
        showReturned: false // 默认不显示已归还的单子
      },
      machineOptions: [], // 设备选项列表
      windowWidth: window.innerWidth,
      temp: {
        id: undefined,
        device_number: '',
        xianyu_order: '',
        send_tracking: '',
        receive_tracking: '',
        send_date: '',
        send_status: '',
        customer_name: '',
        address: '',
        start_date: '',
        end_date: '',
        rent_amount: 0,
        accessories_note: '',
        note: ''
      },
      viewData: {},
      dialogFormVisible: false,
      detailDialogVisible: false,
      dialogStatus: '',
      textMap: {
        update: '编辑租赁记录',
        create: '添加租赁记录'
      },
      rules: {
        start_date: [{ required: true, message: '开始时间是必填项', trigger: 'change' }],
        end_date: [{ required: true, message: '结束时间是必填项', trigger: 'change' }],
        rent_amount: [{ required: true, message: '租金是必填项', trigger: 'blur' }],
        accessories_note: [{ required: true, message: '配件备注是必填项', trigger: 'blur' }],
        send_date: [{ required: true, message: '发货时间是必填项', trigger: 'change' }]
      },
      // 自动设备分配相关数据
      deviceAllocationDialogVisible: false,
      deviceAllocationLoading: false,
      allocationPreview: null,
      allocationConfig: {
        prefer_same_device: true,
        consider_location: false,
        allow_device_upgrade: true,
        buffer_days: 1
      },
      
      // 保留原有自动排单数据以兼容
      autoScheduleDialogVisible: false,
      autoScheduleLoading: false,
      schedulePreview: null,
      scheduleConfig: {
        buffer_days: 1,
        priority_rules: ['time', 'device', 'duration'],
        include_weekends: true,
        max_reschedule_days: 30
      }
    }
  },
  computed: {
    isMobile() {
      return this.windowWidth <= 768
    },
    dialogWidth() {
      if (this.windowWidth <= 480) {
        return '98%'
      } else if (this.windowWidth <= 768) {
        return '95%'
      } else {
        return '60%'
      }
    },
    hasPendingOrders() {
      return this.list.some(item => !item.send_status || item.send_status === '未发货' || item.send_status === '')
    }
  },
  created() {
    this.getList()
    this.getMachineOptions()
  },
  mounted() {
    this.handleResize()
    window.addEventListener('resize', this.handleResize)
  },
  // 页面被keep-alive激活时执行，确保每次进入页面都获取最新数据
  activated() {
    this.getList()
    this.getMachineOptions()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    parseTime,
    statusTagType(status) {
      const statusMap = {
        '未发货': 'danger',
        '已发货': 'success',
        '已收货': 'success',
        '已归还': 'info'  // 修改已归还状态为灰色
      }
      return statusMap[status] || 'danger'
    },
    getMachineOptions() {
      // 获取设备列表用于下拉选择
      getMachines().then(response => {
        console.log('设备列表API响应:', response)
        // 修正：处理新的API响应格式，包含items和total
        if (response && response.items) {
          this.machineOptions = response.items
        } else {
          // 兼容旧格式
          this.machineOptions = Array.isArray(response) ? response : []
        }
      }).catch(error => {
        console.error('获取设备列表失败:', error)
        this.machineOptions = []
      })
    },
    getList() {
      this.listLoading = true
      const params = {
        skip: (this.listQuery.page - 1) * this.listQuery.limit,
        limit: this.listQuery.limit
      }
      
      // 添加排序参数
      if (this.listQuery.sort) {
        params.sort = this.listQuery.sort
        console.log('添加排序参数到API请求:', params.sort)
      }
      
      // 添加搜索参数
      if (this.listQuery.search) {
        params.search = this.listQuery.search
      }

      // 添加状态筛选参数
      if (this.listQuery.status) {
        params.status = this.listQuery.status
      }
      
      // 处理是否显示已归还的单子
      if (!this.listQuery.showReturned) {
        params.exclude_status = '已归还'
      }
      
      console.log('API请求参数:', params)
      
      getRentals(params).then(response => {
        console.log('租赁列表API响应:', response)
        this.list = Array.isArray(response) ? response : []
        this.total = this.list.length // 简化处理
        this.listLoading = false
      }).catch(error => {
        console.error('获取租赁列表失败:', error)
        this.list = []
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        device_number: '',
        xianyu_order: '',
        send_tracking: '',
        receive_tracking: '',
        send_date: '',
        send_status: '未发货',
        customer_name: '',
        address: '',
        start_date: '',
        end_date: '',
        rent_amount: 0,
        accessories_note: '标准配件',
        note: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      // 刷新设备选项列表
      this.getMachineOptions()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          
          // 格式化日期为ISO格式
          if (tempData.send_date && !tempData.send_date.includes('T')) {
            tempData.send_date = `${tempData.send_date}T00:00:00`
          }
          
          createRental(tempData).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '创建成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      // 刷新设备选项列表
      this.getMachineOptions()
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          
          // 格式化日期为ISO格式
          if (tempData.send_date && !tempData.send_date.includes('T')) {
            tempData.send_date = `${tempData.send_date}T00:00:00`
          }
          
          updateRental(tempData.id, tempData).then(() => {
            const index = this.list.findIndex(v => v.id === this.temp.id)
            this.list.splice(index, 1, this.temp)
            this.dialogFormVisible = false
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleView(row) {
      this.viewData = Object.assign({}, row)
      this.detailDialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('确定要删除这条租赁记录吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRental(row.id).then(() => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          const index = this.list.findIndex(v => v.id === row.id)
          this.list.splice(index, 1)
        })
      })
    },
    formatDateTime(dateStr) {
      if (!dateStr) return '-'
      const date = new Date(dateStr)
      return date.toLocaleString()
    },
    formatDate(dateStr) {
      if (!dateStr) return '-'
      const date = new Date(dateStr)
      // 确保返回的格式为YYYY/MM/DD
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}/${month}/${day}`
    },
    handleResize() {
      this.windowWidth = window.innerWidth
    },
    handleDialogClose() {
      // 清除表单数据，确保下次打开时不会有缓存数据
      this.resetTemp()
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].clearValidate()
      }
    },
    handleDetailDialogClose() {
      // 清除详情数据，确保下次打开时不会有缓存数据
      this.viewData = {}
    },
    // 自动设备分配相关方法
    async handleAutoDeviceAllocation() {
      if (!this.hasPendingOrders) {
        this.$message.warning('当前没有未发货的订单需要重新分配设备')
        return
      }
      
      // 显示功能说明提示
      const confirmResult = await this.$confirm(
        '智能设备分配功能将重新分配设备以避免冲突，客户的租赁时间保持完全不变。是否继续？',
        '智能设备分配',
        {
          confirmButtonText: '继续',
          cancelButtonText: '取消',
          type: 'info'
        }
      ).catch(() => false)
      
      if (!confirmResult) {
        return
      }
      
      this.deviceAllocationLoading = true
      try {
        const response = await getAutoDeviceAllocationPreview(this.allocationConfig)
        this.allocationPreview = response
        this.deviceAllocationDialogVisible = true
      } catch (error) {
        console.error('获取智能设备分配预览失败:', error)
        this.$message.error('获取智能设备分配预览失败: ' + (error.response?.data?.detail || error.message))
      } finally {
        this.deviceAllocationLoading = false
      }
    },
    
    async confirmDeviceAllocation() {
      if (!this.allocationPreview || !this.allocationPreview.device_changes) {
        this.$message.error('没有分配数据')
        return
      }
      
      this.deviceAllocationLoading = true
      try {
        const confirmData = {
          device_changes: this.allocationPreview.device_changes.filter(item => item.status === 'success'),
          auto_update_send_status: true
        }
        
        const response = await confirmAutoDeviceAllocation(confirmData)
        this.$message.success(`智能设备分配完成！成功处理 ${response.success_count} 个订单，客户时间保持不变`)
        this.deviceAllocationDialogVisible = false
        this.getList() // 刷新列表
      } catch (error) {
        console.error('确认自动设备分配失败:', error)
        this.$message.error('确认自动设备分配失败: ' + (error.response?.data?.detail || error.message))
      } finally {
        this.deviceAllocationLoading = false
      }
    },
    
    getDeviceChangeStatusType(status) {
      const statusMap = {
        'success': 'success',
        'warning': 'warning', 
        'conflict': 'danger'
      }
      return statusMap[status] || 'info'
    },
    
    getDeviceChangeStatusText(status) {
      const statusTextMap = {
        'success': '成功',
        'warning': '警告',
        'conflict': '冲突'
      }
      return statusTextMap[status] || '未知'
    },
    
    // 保留原有方法以兼容
    async handleAutoSchedule() {
      if (!this.hasPendingOrders) {
        this.$message.warning('当前没有未发货的订单需要排单')
        return
      }
      
      this.autoScheduleLoading = true
      try {
        const response = await getAutoSchedulePreview(this.scheduleConfig)
        this.schedulePreview = response
        this.autoScheduleDialogVisible = true
      } catch (error) {
        console.error('获取自动排单预览失败:', error)
        this.$message.error('获取自动排单预览失败: ' + (error.response?.data?.detail || error.message))
      } finally {
        this.autoScheduleLoading = false
      }
    },
    
    async confirmSchedule() {
      if (!this.schedulePreview || !this.schedulePreview.schedule_changes) {
        this.$message.error('没有排单数据')
        return
      }
      
      this.autoScheduleLoading = true
      try {
        const confirmData = {
          schedule_changes: this.schedulePreview.schedule_changes.filter(item => item.status === 'success'),
          auto_update_send_status: true
        }
        
        const response = await confirmAutoSchedule(confirmData)
        this.$message.success(`自动排单完成！成功处理 ${response.success_count} 个订单`)
        this.autoScheduleDialogVisible = false
        this.getList() // 刷新列表
      } catch (error) {
        console.error('确认自动排单失败:', error)
        this.$message.error('确认自动排单失败: ' + (error.response?.data?.detail || error.message))
      } finally {
        this.autoScheduleLoading = false
      }
    },
    
    getScheduleChangeStatusType(status) {
      const statusMap = {
        'success': 'success',
        'warning': 'warning', 
        'conflict': 'danger'
      }
      return statusMap[status] || 'info'
    },
    
    getScheduleChangeStatusText(status) {
      const statusTextMap = {
        'success': '成功',
        'warning': '警告',
        'conflict': '冲突'
      }
      return statusTextMap[status] || '未知'
    },
    
    // 查询物流方法
    queryLogistics(trackingNumber) {
      if (!trackingNumber) {
        this.$message.warning('快递单号为空')
        return
      }
      
      // 构建物流查询URL，将快递单号作为参数
      const logisticsUrl = `https://www.sf-express.com/chn/sc/waybill/waybill-detail/${trackingNumber}`
      
      // 在新窗口中打开物流查询页面
      window.open(logisticsUrl, '_blank')
    },
    handleSortChange({ prop, order }) {
      console.log('排序变更:', { prop, order })
      
      // 映射前端属性名到后端字段名
      const fieldMapping = {
        'start_date': 'start_date',
        'send_status': 'send_status',
        'send_date': 'send_date'
      }
      
      if (!order) {
        // 取消排序
        this.listQuery.sort = ''
      } else {
        const backendField = fieldMapping[prop]
        if (backendField) {
          this.listQuery.sort = order === 'ascending' ? backendField : `-${backendField}`
        }
      }
      
      console.log('排序参数:', this.listQuery.sort)
      this.getList()
    },
    
    // 判断发货时间单元格是否需要高亮显示
    isHighlightedSendDate(row) {
      // 检查发货状态是否为未发货
      if (row.send_status === '未发货' || !row.send_status) {
        if (!row.send_date) return false
        
        // 获取当前日期（年月日）
        const today = new Date()
        const todayYear = today.getFullYear()
        const todayMonth = today.getMonth() + 1
        const todayDay = today.getDate()
        
        // 解析行中的日期，处理ISO格式 "2025-07-23T00:00:00"
        const rowDate = new Date(row.send_date)
        const rowYear = rowDate.getFullYear()
        const rowMonth = rowDate.getMonth() + 1
        const rowDay = rowDate.getDate()
        
        // 比较年月日是否相同
        if (rowYear === todayYear && rowMonth === todayMonth && rowDay === todayDay) {
          console.log('匹配到需要标红的行:', row)
          return true
        }
      }
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.detail-table td {
  padding: 12px 15px;
  border: 1px solid #EBEEF5;
  vertical-align: top;
}

.detail-table .label {
  background-color: #FAFAFA;
  font-weight: bold;
  color: #606266;
  width: 120px;
  text-align: right;
}

.detail-table .value {
  color: #303133;
  word-break: break-all;
}

.detail-table tr:nth-child(even) {
  background-color: #FAFAFA;
}

.detail-table tr:hover {
  background-color: #F5F7FA;
}

/* 自动排单样式 */
.schedule-summary {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
}

.summary-item {
  text-align: center;
  padding: 15px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-item.success {
  border-left: 4px solid #67c23a;
}

.summary-item.warning {
  border-left: 4px solid #e6a23c;
}

.summary-item.danger {
  border-left: 4px solid #f56c6c;
}

.summary-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.summary-label {
  font-size: 14px;
  color: #606266;
}

.schedule-changes h4 {
  margin-bottom: 10px;
  color: #303133;
}

.text-changed {
  color: #409eff;
  font-weight: bold;
}

.allocation-summary {
  margin-bottom: 20px;
  
  .summary-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    
    &.success {
      background: #f0f9ff;
      border-color: #67e8f9;
    }
    
    &.warning {
      background: #fffbeb;
      border-color: #fed7aa;
    }
    
    &.danger {
      background: #fef2f2;
      border-color: #fecaca;
    }
    
    .summary-number {
      font-size: 24px;
      font-weight: bold;
      color: #333;
    }
    
    .summary-label {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }
  }
}

.device-changes {
  .text-changed {
    color: #f56c6c;
    font-weight: bold;
  }
}

.allocation-config {
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
  
  h4 {
    margin: 0 0 15px 0;
    color: #333;
  }
}

/* 发货时间高亮样式 */
.highlight-cell {
  background-color: #ffcccc !important;
  padding: 8px;
  border-radius: 4px;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.highlight-cell {
  background-color: #ffcccc;
  padding: 8px;
  border-radius: 4px;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 