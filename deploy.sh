#!/bin/bash

# 租赁管理系统部署脚本
# 使用方法: ./deploy.sh

set -e

echo "🚀 开始部署租赁管理系统..."

# 检查是否在项目根目录
if [ ! -f "package.json" ] || [ ! -d "backend" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    exit 1
fi

# 1. 构建前端
echo "📦 构建前端项目..."
npm run build:prod

# 2. 创建部署目录
echo "📁 创建部署目录..."
sudo mkdir -p /var/www/dazuizulin
sudo cp -r dist/* /var/www/dazuizulin/

# 3. 设置权限
echo "🔐 设置文件权限..."
sudo chown -R www-data:www-data /var/www/dazuizulin
sudo chmod -R 755 /var/www/dazuizulin

# 4. 安装后端依赖
echo "🐍 安装后端依赖..."
cd backend
pip install -r requirements.txt

# 5. 启动后端服务
echo "🔧 启动后端服务..."
# 使用nohup在后台运行
nohup python start.py > ../backend.log 2>&1 &
BACKEND_PID=$!
echo "✅ 后端服务已启动，PID: $BACKEND_PID"

# 6. 配置nginx
echo "🌐 配置nginx..."
sudo cp ../nginx.conf /etc/nginx/nginx.conf

# 7. 测试nginx配置
echo "🔍 测试nginx配置..."
sudo nginx -t

if [ $? -eq 0 ]; then
    echo "✅ nginx配置测试通过"
    
    # 8. 重启nginx
    echo "🔄 重启nginx服务..."
    sudo systemctl restart nginx
    
    echo "🎉 部署完成！"
    echo "📱 前端地址: http://localhost"
    echo "🔗 API地址: http://localhost/api"
    echo "💚 健康检查: http://localhost/health"
    echo "📋 后端日志: backend.log"
    echo "🛑 停止后端服务: kill $BACKEND_PID"
else
    echo "❌ nginx配置测试失败"
    exit 1
fi 