import request from '@/utils/request'

// 获取话术列表
export function getScripts(params) {
  return request({
    url: '/scripts',
    method: 'get',
    params
  })
}

// 创建话术
export function createScript(data) {
  return request({
    url: '/scripts',
    method: 'post',
    data
  })
}

// 获取单个话术
export function getScript(id) {
  return request({
    url: `/scripts/${id}`,
    method: 'get'
  })
}

// 更新话术
export function updateScript(id, data) {
  return request({
    url: `/scripts/${id}`,
    method: 'put',
    data
  })
}

// 删除话术
export function deleteScript(id) {
  return request({
    url: `/scripts/${id}`,
    method: 'delete'
  })
}

// 复制话术
export function copyScript(id) {
  return request({
    url: `/scripts/${id}/copy`,
    method: 'post'
  })
}

// 获取话术内容用于复制到剪贴板
export function getScriptContent(id) {
  return request({
    url: `/scripts/${id}/copy-to-clipboard`,
    method: 'get'
  })
}


