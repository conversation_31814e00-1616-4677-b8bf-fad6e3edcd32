from sqlalchemy.orm import Session
from models.user import User
from models.machine import Machine
from models.rental import Rental
from app.auth import get_password_hash
from datetime import date, datetime, timedelta

def create_default_admin(db: Session):
    """创建默认管理员账号"""
    # 检查是否已存在管理员账号
    admin_user = db.query(User).filter(User.username == "admin").first()
    if not admin_user:
        # 创建默认管理员
        hashed_password = get_password_hash("Admin@2024#Secure!")
        admin_user = User(
            username="admin",
            password=hashed_password
        )
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        print("✅ 已创建默认管理员账号:")
        print("   用户名: admin")
        print("   密码: Admin@2024#Secure!")
        print("   建议首次登录后修改密码！")
        return admin_user
    else:
        print("ℹ️  管理员账号已存在")
        return admin_user

def create_sample_data(db: Session, user: User):
    """为用户创建示例数据"""
    # 检查是否已有该用户的机器数据
    existing_machines = db.query(Machine).filter(Machine.user_id == user.id).count()
    if existing_machines > 0:
        print("ℹ️  用户示例数据已存在，跳过创建")
        return
    
    print(f"🔧 为用户 {user.username} 创建示例数据...")
    
    # 创建示例机器
    sample_machines = [
        {
            "device_number": "DEV001",
            "sn_code": "SN12345001",
            "purchase_date": date(2023, 1, 15),
            "warranty_expire_date": date(2025, 1, 15),
        },
        {
            "device_number": "DEV002", 
            "sn_code": "SN12345002",
            "purchase_date": date(2023, 3, 20),
            "warranty_expire_date": date(2025, 3, 20),
        },
        {
            "device_number": "DEV003",
            "sn_code": "SN12345003", 
            "purchase_date": date(2023, 6, 10),
            "warranty_expire_date": date(2025, 6, 10),
        }
    ]
    
    machines = []
    for machine_data in sample_machines:
        machine = Machine(
            **machine_data,
            user_id=user.id
        )
        db.add(machine)
        machines.append(machine)
    
    db.commit()
    
    # 创建示例租赁记录
    today = date.today()
    sample_rentals = [
        # 已发货的租赁记录（用于占用设备时间）
        {
            "device_number": "DEV001",
            "xianyu_order": "XY202310001",
            "customer_name": "张三",
            "address": "北京市朝阳区xxx街道xxx号",
            "start_date": today - timedelta(days=10),
            "end_date": today + timedelta(days=5),
            "rent_amount": 299.0,
            "accessories_note": "充电器、说明书、包装盒",
            "send_status": "已发货",
            "note": "客户要求加急发货"
        },
        # 未发货的租赁记录（用于测试自动排单）
        {
            "device_number": "DEV001",
            "xianyu_order": "XY202310002", 
            "customer_name": "李四",
            "address": "上海市浦东新区xxx路xxx号",
            "start_date": today + timedelta(days=1),
            "end_date": today + timedelta(days=8),
            "rent_amount": 399.0,
            "accessories_note": "全套配件",
            "send_status": "未发货",
            "note": "VIP客户"
        },
        {
            "device_number": "DEV001",
            "xianyu_order": "XY202310003",
            "customer_name": "王五", 
            "address": "广州市天河区xxx大道xxx号",
            "start_date": today + timedelta(days=3),
            "end_date": today + timedelta(days=10),
            "rent_amount": 299.0,
            "accessories_note": "基础配件",
            "send_status": "未发货",
            "note": "需要自动排单调整时间"
        },
        {
            "device_number": "DEV002",
            "xianyu_order": "XY202310004",
            "customer_name": "赵六", 
            "address": "深圳市南山区xxx路xxx号",
            "start_date": today + timedelta(days=2),
            "end_date": today + timedelta(days=9),
            "rent_amount": 199.0,
            "accessories_note": "标准配件",
            "send_status": "未发货",
            "note": "普通客户"
        },
        # 已归还的历史记录
        {
            "device_number": "DEV003",
            "xianyu_order": "XY202309005",
            "customer_name": "孙七", 
            "address": "杭州市西湖区xxx街xxx号",
            "start_date": today - timedelta(days=20),
            "end_date": today - timedelta(days=5),
            "rent_amount": 199.0,
            "accessories_note": "基础配件",
            "send_status": "已归还",
            "note": "正常租赁"
        }
    ]
    
    for rental_data in sample_rentals:
        rental = Rental(
            **rental_data,
            user_id=user.id
        )
        db.add(rental)
    
    db.commit()
    print("✅ 示例数据创建完成!")

def migrate_existing_data(db: Session):
    """迁移现有数据，为没有user_id的记录分配给admin用户"""
    admin_user = db.query(User).filter(User.username == "admin").first()
    if not admin_user:
        print("⚠️  未找到admin用户，跳过数据迁移")
        return
    
    # 迁移机器数据
    machines_without_user = db.query(Machine).filter(Machine.user_id == None).all()
    if machines_without_user:
        print(f"🔄 迁移 {len(machines_without_user)} 条机器记录到admin用户...")
        for machine in machines_without_user:
            machine.user_id = admin_user.id
        db.commit()
    
    # 迁移租赁数据
    rentals_without_user = db.query(Rental).filter(Rental.user_id == None).all()
    if rentals_without_user:
        print(f"🔄 迁移 {len(rentals_without_user)} 条租赁记录到admin用户...")
        for rental in rentals_without_user:
            rental.user_id = admin_user.id
        db.commit()
    
    if machines_without_user or rentals_without_user:
        print("✅ 数据迁移完成!")

def init_database_data(db: Session):
    """初始化数据库数据"""
    # 创建默认管理员
    admin_user = create_default_admin(db)
    
    # 迁移现有数据
    # migrate_existing_data(db)
    
    # # 为管理员创建示例数据（如果还没有的话）
    # create_sample_data(db, admin_user) 