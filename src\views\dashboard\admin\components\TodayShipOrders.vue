<template>
  <el-card class="order-list-card">
    <div slot="header" class="clearfix">
      <span class="card-title">今天应发租赁订单</span>
      <div class="header-actions">
        <el-badge :value="orders.length" class="order-count" type="primary" />
        <el-button 
          type="text" 
          size="mini" 
          icon="el-icon-refresh" 
          @click="fetchOrders"
          :loading="loading"
          style="margin-left: 10px;"
        >
          刷新
        </el-button>
      </div>
    </div>
    
    <div v-if="loading" class="loading-container">
      <i class="el-icon-loading"></i>
      <p>加载中...</p>
    </div>
    
    <div v-else-if="orders.length === 0" class="empty-container">
      <i class="el-icon-box"></i>
      <p>今天暂无应发货订单</p>
    </div>
    
    <div v-else class="order-list">
      <div 
        v-for="order in orders" 
        :key="order.id" 
        class="order-item"
        @click="viewOrderDetail(order)"
      >
        <div class="order-info">
          <div class="order-header">
            <span class="order-number">{{ order.xianyu_order || `订单#${order.id}` }}</span>
            <el-tag 
              :type="getStatusType(order.send_status)" 
              size="mini"
            >
              {{ order.send_status || '未发货' }}
            </el-tag>
          </div>
          <div class="order-details">
            <p><strong>客户：</strong>{{ order.customer_name || '未填写' }}</p>
            <p><strong>设备：</strong>{{ order.device_number || '未分配' }}</p>
            <p><strong>快递单号：</strong>{{ order.send_tracking || '未填写' }}</p>
            <p><strong>租金：</strong>¥{{ order.rent_amount || 0 }}</p>
          </div>
        </div>
        <div class="order-actions">
          <el-button 
            type="primary" 
            size="mini"
            @click.stop="handleShip(order)"
            :disabled="order.send_status === '已发货'"
          >
            确认发货
          </el-button>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { getTodayShipOrders } from '@/api/dashboard'

export default {
  name: 'TodayShipOrders',
  data() {
    return {
      orders: [],
      loading: false
    }
  },
  mounted() {
    this.fetchOrders()
  },
  methods: {
    async fetchOrders() {
      this.loading = true
      try {
        const response = await getTodayShipOrders()
        this.orders = response || []
      } catch (error) {
        console.error('获取今天应发订单失败:', error)
        this.$message.error('获取订单列表失败')
        this.orders = []
      } finally {
        this.loading = false
      }
    },
    getStatusType(status) {
      const statusMap = {
        '已发货': 'success',
        '已收货': 'info',
        '已归还': 'info',
        '未发货': 'warning',
        '待发货': 'warning'
      }
      return statusMap[status] || 'info'
    },
    viewOrderDetail(order) {
      // 跳转到租赁管理页面查看详情
      this.$router.push({
        path: '/rental/list',
        query: { search: order.xianyu_order || order.id }
      })
    },
    handleShip(order) {
      // 这里可以集成发货功能，现在先显示消息
      this.$message.info('发货功能待集成，请到租赁管理页面操作')
      this.viewOrderDetail(order)
    }
  }
}
</script>

<style lang="scss" scoped>
.order-list-card {
  margin-bottom: 20px;
  
  .card-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
  
  .header-actions {
    float: right;
    display: flex;
    align-items: center;
  }
  
  .order-count {
    margin-top: 2px;
  }
}

.loading-container,
.empty-container {
  text-align: center;
  padding: 40px 0;
  color: #909399;
  
  i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
  }
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

.order-list {
  max-height: 400px;
  overflow-y: auto;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.order-info {
  flex: 1;
  
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    .order-number {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }
  }
  
  .order-details {
    p {
      margin: 5px 0;
      font-size: 13px;
      color: #606266;
      
      strong {
        color: #303133;
        margin-right: 5px;
      }
    }
  }
}

.order-actions {
  margin-left: 15px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

// 移动端适配
@media (max-width: 768px) {
  .order-item {
    flex-direction: column;
    
    .order-actions {
      margin-left: 0;
      margin-top: 10px;
      flex-direction: row;
    }
  }
}
</style> 