<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.search"
        placeholder="搜索姓名"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="success"
        icon="el-icon-refresh"
        @click="resetFilter"
      >
        重置
      </el-button>
    </div>

    <!-- 快速查询 -->
    <!--
    <div class="quick-search-container">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>快速查询</span>
        </div>
        <div class="quick-search-form">
          <el-input
            v-model="quickSearchName"
            placeholder="输入姓名进行快速查询"
            style="width: 300px;"
            @keyup.enter.native="quickSearch"
          />
          <el-button
            type="primary"
            style="margin-left: 10px;"
            @click="quickSearch"
          >
            查询
          </el-button>
        </div>
        <div v-if="quickSearchResult" class="quick-search-result">
          <el-alert
            :title="quickSearchResult.message"
            :type="quickSearchResult.is_blacklisted ? 'error' : 'success'"
            :closable="false"
            show-icon
          />
        </div>
      </el-card>
    </div>
    -->

    <!-- 统计信息 -->
    <div class="stats-container">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="box-card">
            <div class="stats-item">
              <div class="stats-number">{{ total }}</div>
              <div class="stats-label">黑名单总数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="box-card">
            <div class="stats-item">
              <div class="stats-number">{{ filteredTotal }}</div>
              <div class="stats-label">当前显示</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="box-card">
            <div class="stats-item">
              <div class="stats-number">{{ totalPages }}</div>
              <div class="stats-label">总页数</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 黑名单列表 -->
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;"
    >
      <el-table-column
        label="序号"
        type="index"
        width="80"
        align="center"
        :index="indexMethod"
      />
      <el-table-column
        label="姓名"
        prop="name"
        align="center"
      >
        <template slot-scope="scope">
          <span class="blacklist-name">{{ scope.row }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="风险等级"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <el-tag type="danger" size="small">高风险</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        align="center"
      >
        <template slot-scope="scope">
          <span>租赁黑名单人员，请谨慎处理</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="filteredTotal"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.page_size"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getBlacklist, searchBlacklist } from '@/api/blacklist'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'

export default {
  name: 'BlacklistIndex',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      filteredTotal: 0,
      totalPages: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        page_size: 20,
        search: ''
      },
      quickSearchName: '',
      quickSearchResult: null
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
             getBlacklist(this.listQuery).then(response => {
         if (response.code === 200) {
           this.list = response.data.list
           this.total = response.data.total
           this.filteredTotal = response.data.total
           this.totalPages = response.data.total_pages
           
           // 显示过滤后的总数
           this.filteredTotal = response.data.total
         } else {
           this.$message.error(response.message || '获取黑名单失败')
         }
         this.listLoading = false
       }).catch(error => {
         console.error('获取黑名单失败:', error)
         this.$message.error('获取黑名单失败')
         this.listLoading = false
       })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetFilter() {
      this.listQuery.search = ''
      this.listQuery.page = 1
      this.quickSearchName = ''
      this.quickSearchResult = null
      this.getList()
    },
    quickSearch() {
      if (!this.quickSearchName.trim()) {
        this.$message.warning('请输入要查询的姓名')
        return
      }
      
      searchBlacklist(this.quickSearchName.trim()).then(response => {
        if (response.code === 200) {
          this.quickSearchResult = response.data
        } else {
          this.$message.error(response.message || '查询失败')
        }
      }).catch(error => {
        console.error('查询失败:', error)
        this.$message.error('查询失败')
      })
    },
    indexMethod(index) {
      return (this.listQuery.page - 1) * this.listQuery.page_size + index + 1
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  .filter-container {
    padding-bottom: 10px;
    .filter-item {
      display: inline-block;
      vertical-align: middle;
      margin-bottom: 10px;
    }
  }
  
  .quick-search-container {
    margin-bottom: 20px;
    
    .quick-search-form {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }
    
    .quick-search-result {
      margin-top: 10px;
    }
  }
  
  .stats-container {
    margin-bottom: 20px;
    
    .stats-item {
      text-align: center;
      
      .stats-number {
        font-size: 32px;
        font-weight: bold;
        color: #409EFF;
        margin-bottom: 8px;
      }
      
      .stats-label {
        font-size: 14px;
        color: #666;
      }
    }
  }
  
  .blacklist-name {
    font-weight: bold;
    color: #E6A23C;
  }
}
</style> 