# 姓名提取工具

这个工具可以从文本文件中提取中文姓名并保存到CSV文件中。

## 文件说明

- `extract_names.py` - 基础版本的姓名提取脚本
- `extract_names_advanced.py` - 增强版本，提供详细统计信息和姓氏分析
- `extracted_names.csv` - 提取的姓名列表（包含序号、姓名、字符数）
- `extracted_names_surname_analysis.csv` - 姓氏分析报告

## 使用方法

### 基础版本
```bash
python extract_names.py
```

### 增强版本
```bash
# 基本使用
python extract_names_advanced.py

# 生成姓氏分析
python extract_names_advanced.py --surname-analysis

# 自定义输入输出文件
python extract_names_advanced.py input.txt output.csv

# 设置姓名长度范围（只提取2-3字的姓名）
python extract_names_advanced.py --min-length 2 --max-length 3

# 不显示统计信息
python extract_names_advanced.py --no-stats
```

## 提取结果统计

从 `backend/PixPin_2025-07-18_01-24-52.txt` 文件中提取的结果：

- **总行数**: 1,601 行
- **识别为姓名的行数**: 906 行
- **唯一姓名数量**: 871 个
- **重复姓名数量**: 35 个

### 姓名长度分布
- 2字姓名: 236 个 (27.1%)
- 3字姓名: 629 个 (72.2%)
- 4字姓名: 6 个 (0.7%)

### 最常见的姓氏（前10个）
1. 李: 70 个 (8.0%)
2. 张: 60 个 (6.9%)
3. 王: 55 个 (6.3%)
4. 刘: 52 个 (6.0%)
5. 陈: 34 个 (3.9%)
6. 杨: 25 个 (2.9%)
7. 黄: 21 个 (2.4%)
8. 周: 19 个 (2.2%)
9. 赵: 19 个 (2.2%)
10. 朱: 17 个 (2.0%)

### 重复出现的姓名
- 朱斌、邓世奇、崔振宇、王梦鸽、王璐豪、潘红林、李小源、杨世超、张恒、黄海龙 等姓名在原文件中出现了2次

## 功能特点

1. **自动识别中文姓名** - 使用正则表达式匹配2-4个中文字符的姓名
2. **去重处理** - 自动去除重复的姓名
3. **统计分析** - 提供详细的统计信息
4. **姓氏分析** - 分析姓氏分布情况
5. **CSV输出** - 结果保存为CSV格式，方便后续处理
6. **UTF-8编码** - 支持中文字符的正确显示

## 注意事项

- 脚本会自动跳过包含数字、字母或特殊字符的行
- 只提取纯中文字符组成的姓名
- 默认提取2-4个字符长度的姓名
- 输出的CSV文件使用UTF-8-BOM编码，确保Excel等软件能正确显示中文 