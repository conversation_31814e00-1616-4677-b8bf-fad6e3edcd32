from sqlalchemy import Column, Integer, String, DateTime, Date, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from database.database import Base

class Machine(Base):
    __tablename__ = "machines"
    
    id = Column(Integer, primary_key=True, index=True)
    device_number = Column(String(100), index=True, nullable=False)
    sn_code = Column(String(100), nullable=False)
    device_type = Column(String(100), nullable=True, default="通用设备")  # 设备类型，用于同类型替换
    purchase_date = Column(Date, nullable=False)
    warranty_expire_date = Column(Date, nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联用户表
    owner = relationship("User", foreign_keys=[user_id]) 