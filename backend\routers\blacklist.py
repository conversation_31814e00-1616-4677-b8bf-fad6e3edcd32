from fastapi import APIRouter, Query
from typing import List, Optional
import os

router = APIRouter()

@router.get("/blacklist")
async def get_blacklist(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词")
):
    """
    获取租赁黑名单数据
    """
    try:
        # 读取骗子姓名.txt文件
        file_path = os.path.join(os.path.dirname(__file__), "..", "骗子姓名.txt")
        with open(file_path, 'r', encoding='utf-8') as f:
            names = [line.strip() for line in f.readlines() if line.strip()]
        
        # 如果有搜索关键词，进行过滤
        if search:
            names = [name for name in names if search in name]
        
        # 分页处理
        total = len(names)
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        page_names = names[start_index:end_index]
        
        return {
            "code": 200,
            "message": "获取成功",
            "data": {
                "list": page_names,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
        }
    except Exception as e:
        return {
            "code": 500,
            "message": f"获取黑名单数据失败: {str(e)}",
            "data": None
        }

@router.get("/blacklist/search")
async def search_blacklist(name: str = Query(..., description="要查询的姓名")):
    """
    查询指定姓名是否在黑名单中
    """
    try:
        # 读取骗子姓名.txt文件
        file_path = os.path.join(os.path.dirname(__file__), "..", "骗子姓名.txt")
        with open(file_path, 'r', encoding='utf-8') as f:
            names = [line.strip() for line in f.readlines() if line.strip()]
        
        is_blacklisted = name in names
        
        return {
            "code": 200,
            "message": "查询成功",
            "data": {
                "name": name,
                "is_blacklisted": is_blacklisted,
                "message": "该姓名在黑名单中" if is_blacklisted else "该姓名不在黑名单中"
            }
        }
    except Exception as e:
        return {
            "code": 500,
            "message": f"查询失败: {str(e)}",
            "data": None
        } 