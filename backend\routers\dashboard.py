from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, extract, or_
from database.database import get_db
from models.rental import Rental
from models.machine import Machine
from models.user import User
from app.auth import get_current_user
from datetime import datetime, date, timedelta
import calendar

router = APIRouter(prefix="/dashboard", tags=["仪表盘"])

@router.get("/stats")
def get_dashboard_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取仪表盘统计数据"""
    
    # 当前用户的设备总数
    total_machines = db.query(Machine).filter(Machine.user_id == current_user.id).count()
    
    # 活跃租赁数 (当前日期在租赁期间内的租赁记录)
    today = date.today()
    active_rentals = db.query(Rental).filter(
        and_(
            Rental.start_date <= today,
            Rental.end_date >= today,
            Rental.user_id == current_user.id
        )
    ).count()
    
    # 总共收入 (所有租赁记录rent_amount总和)
    monthly_revenue = db.query(func.sum(Rental.rent_amount)).filter(
        Rental.user_id == current_user.id
    ).scalar() or 0
    
    # 空闲机器数 (今天没有订单占用的机器数量)
    # 获取所有用户机器
    all_machines = db.query(Machine).filter(Machine.user_id == current_user.id).all()
    
    # 获取今天有占用的机器设备编号
    occupied_device_numbers = db.query(Rental.device_number).filter(
        and_(
            Rental.start_date <= today,
            Rental.end_date >= today,
            Rental.user_id == current_user.id,
            Rental.device_number.isnot(None)
        )
    ).distinct().all()
    
    # 转换为集合
    occupied_device_set = {device[0] for device in occupied_device_numbers if device[0]}
    
    # 计算空闲机器数
    idle_machines = sum(1 for machine in all_machines if machine.device_number not in occupied_device_set)
    
    # 未发货订单数 (send_status为未发货的记录数)
    pending_send_orders = db.query(Rental).filter(
        and_(
            Rental.send_status == "未发货",
            Rental.user_id == current_user.id
        )
    ).count()
    
    return {
        "total_machines": total_machines,
        "active_rentals": active_rentals,
        "monthly_revenue": float(monthly_revenue),  # 总收入
        "idle_machines": idle_machines,  # 空闲机器数
        "pending_send_orders": pending_send_orders
    }

@router.get("/recent-rentals")
def get_recent_rentals(
    limit: int = 8,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """获取最近的租赁记录"""
    
    # 只返回当前用户的租赁记录
    rentals = db.query(Rental).filter(
        Rental.user_id == current_user.id
    ).order_by(Rental.created_at.desc()).limit(limit).all()
    
    result = []
    for rental in rentals:
        result.append({
            "id": rental.id,
            "device_number": rental.device_number,
            "xianyu_order": rental.xianyu_order,
            "customer_name": rental.customer_name,
            "rent_amount": float(rental.rent_amount) if rental.rent_amount else 0,
            "send_status": rental.send_status,
            "start_date": rental.start_date.isoformat() if rental.start_date else None,
            "end_date": rental.end_date.isoformat() if rental.end_date else None,
            "created_at": rental.created_at.isoformat() if rental.created_at else None
        })
    
    return result

@router.get("/today-ship-orders")
def get_today_ship_orders(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """获取今天应发租赁订单列表"""
    
    today = date.today()
    
    # 查询租赁订单发货时间等于今天的订单，或者开始日期是今天且未发货的订单
    orders = db.query(Rental).filter(
        and_(
            Rental.user_id == current_user.id,
            or_(
                # 发货时间等于今天
                func.date(Rental.send_date) == today,
                # 或者开始日期是今天且还未发货
                and_(
                    Rental.start_date == today,
                    or_(
                        Rental.send_status == "未发货",
                        Rental.send_status == "待发货",
                        Rental.send_status.is_(None),
                        Rental.send_status == ""
                    )
                )
            )
        )
    ).order_by(Rental.created_at.desc()).all()
    
    result = []
    for order in orders:
        result.append({
            "id": order.id,
            "device_number": order.device_number,
            "xianyu_order": order.xianyu_order,
            "customer_name": order.customer_name,
            "address": order.address,
            "send_tracking": order.send_tracking,
            "send_status": order.send_status,
            "rent_amount": float(order.rent_amount) if order.rent_amount else 0,
            "start_date": order.start_date.isoformat() if order.start_date else None,
            "end_date": order.end_date.isoformat() if order.end_date else None,
            "send_date": order.send_date.isoformat() if order.send_date else None,
            "accessories_note": order.accessories_note,
            "note": order.note,
            "created_at": order.created_at.isoformat() if order.created_at else None
        })
    
    return result

@router.get("/return-reminder-orders")
def get_return_reminder_orders(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """获取催退回订单列表"""
    
    # 明天的日期（租赁订单结束日期距离今天还有一天）
    tomorrow = date.today() + timedelta(days=1)
    
    # 查询结束日期是明天的订单（还没有归还的）
    orders = db.query(Rental).filter(
        and_(
            Rental.end_date == tomorrow,
            Rental.send_status != "已归还",
            Rental.user_id == current_user.id
        )
    ).order_by(Rental.created_at.desc()).all()
    
    result = []
    for order in orders:
        result.append({
            "id": order.id,
            "device_number": order.device_number,
            "xianyu_order": order.xianyu_order,
            "customer_name": order.customer_name,
            "address": order.address,
            "receive_tracking": order.receive_tracking,
            "send_status": order.send_status,
            "rent_amount": float(order.rent_amount) if order.rent_amount else 0,
            "start_date": order.start_date.isoformat() if order.start_date else None,
            "end_date": order.end_date.isoformat() if order.end_date else None,
            "send_date": order.send_date.isoformat() if order.send_date else None,
            "accessories_note": order.accessories_note,
            "note": order.note,
            "created_at": order.created_at.isoformat() if order.created_at else None
        })
    
    return result

@router.get("/trend-data")
def get_trend_data(
    days: int = 7,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """获取趋势数据 (最近N天的统计)"""
    
    end_date = date.today()
    start_date = end_date - timedelta(days=days-1)
    
    # 初始化数据结构
    machines_data = {"expected": [], "actual": []}
    rentals_data = {"expected": [], "actual": []}
    revenue_data = {"expected": [], "actual": []}
    pending_data = {"expected": [], "actual": []}
    
    # 获取每天的数据
    for i in range(days):
        current_date = start_date + timedelta(days=i)
        
        # 当前用户截止到当天的设备总数
        machine_count = db.query(Machine).filter(
            and_(
                func.date(Machine.created_at) <= current_date,
                Machine.user_id == current_user.id
            )
        ).count()
        
        # 当前用户当天活跃租赁数
        rental_count = db.query(Rental).filter(
            and_(
                Rental.start_date <= current_date,
                Rental.end_date >= current_date,
                Rental.user_id == current_user.id
            )
        ).count()
        
        # 当前用户当天收入
        daily_revenue = db.query(func.sum(Rental.rent_amount)).filter(
            and_(
                func.date(Rental.start_date) == current_date,
                Rental.user_id == current_user.id
            )
        ).scalar() or 0
        
        # 当前用户当天待发货订单数
        pending_count = db.query(Rental).filter(
            and_(
                func.date(Rental.created_at) == current_date,
                Rental.send_status == "待发货",
                Rental.user_id == current_user.id
            )
        ).count()
        
        # 实际数据
        machines_data["actual"].append(machine_count)
        rentals_data["actual"].append(rental_count)
        revenue_data["actual"].append(float(daily_revenue))
        pending_data["actual"].append(pending_count)
        
        # 预期数据 (简单的增长趋势模拟)
        machines_data["expected"].append(min(machine_count + 1, machine_count + 2))
        rentals_data["expected"].append(max(rental_count, rental_count + 1))
        revenue_data["expected"].append(float(daily_revenue) * 1.1)
        pending_data["expected"].append(max(0, pending_count - 1))
    
    return {
        "machines": {
            "expectedData": machines_data["expected"],
            "actualData": machines_data["actual"]
        },
        "rentals": {
            "expectedData": rentals_data["expected"],
            "actualData": rentals_data["actual"]
        },
        "revenue": {
            "expectedData": revenue_data["expected"],
            "actualData": revenue_data["actual"]
        },
        "pending": {
            "expectedData": pending_data["expected"],
            "actualData": pending_data["actual"]
        }
    }

@router.get("/todo-list")
def get_todo_list(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """生成基于实际数据的待办事项"""
    
    todos = []
    
    # 检查当前用户的待发货订单
    pending_orders = db.query(Rental).filter(
        and_(
            Rental.send_status == "待发货",
            Rental.user_id == current_user.id
        )
    ).all()
    for order in pending_orders:
        todos.append({
            "text": f"处理{order.xianyu_order or '订单'}发货 - {order.customer_name or '客户'}",
            "done": False,
            "priority": "high",
            "type": "order"
        })
    
    # 检查当前用户即将到期的租赁
    next_week = date.today() + timedelta(days=7)
    expiring_rentals = db.query(Rental).filter(
        and_(
            Rental.end_date <= next_week,
            Rental.end_date >= date.today(),
            Rental.send_status != "已归还",
            Rental.user_id == current_user.id
        )
    ).all()
    
    for rental in expiring_rentals:
        todos.append({
            "text": f"联系{rental.customer_name or '客户'}确认{rental.device_number or '设备'}归还",
            "done": False,
            "priority": "medium",
            "type": "return"
        })
    
    # 检查当前用户逾期租赁
    overdue_rentals = db.query(Rental).filter(
        and_(
            Rental.end_date < date.today(),
            Rental.send_status != "已归还",
            Rental.user_id == current_user.id
        )
    ).all()
    
    for rental in overdue_rentals:
        todos.append({
            "text": f"紧急处理{rental.device_number or '设备'}逾期 - {rental.customer_name or '客户'}",
            "done": False,
            "priority": "urgent",
            "type": "overdue"
        })
    
    # 添加一些通用待办事项
    todos.extend([
        {
            "text": "更新设备维护记录",
            "done": False,
            "priority": "low",
            "type": "maintenance"
        },
        {
            "text": "整理总收入统计",
            "done": True,
            "priority": "low",
            "type": "report"
        }
    ])
    
    return todos[:10]  # 最多返回10项 