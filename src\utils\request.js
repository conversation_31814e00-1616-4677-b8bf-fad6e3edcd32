import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 5000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    console.log('API请求配置:', {
      url: config.url,
      method: config.method,
      params: config.params,
      data: config.data
    })

    if (store.getters.token) {
      // use Bearer token for FastAPI
      config.headers['Authorization'] = `Bearer ${getToken()}`
    }
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    const res = response.data

    // FastAPI直接返回数据，直接返回原始数据
    return res
  },
  error => {
    console.log('err' + error) // for debug
    let message = error.message
    
    // 处理HTTP状态码错误
    if (error.response) {
      const status = error.response.status
      if (status === 401) {
        message = '认证失败，请重新登录'
        // 清除token并跳转到登录页
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      } else if (status === 403) {
        message = '权限不足'
      } else if (status === 404) {
        message = '请求的资源不存在'
      } else if (status === 422) {
        message = error.response.data.detail || '请求参数错误'
      } else if (status >= 500) {
        message = '服务器错误'
      }
    }
    
    Message({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
