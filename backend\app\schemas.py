from pydantic import BaseModel
from datetime import date, datetime
from typing import Optional, List

# 用户相关模式
class UserBase(BaseModel):
    username: str

class UserCreate(UserBase):
    password: str

class User(UserBase):
    id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class UserLogin(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

# 机器相关模式
class MachineBase(BaseModel):
    device_number: str
    sn_code: str
    device_type: Optional[str] = "通用设备"  # 设备类型
    purchase_date: date
    warranty_expire_date: date

class MachineCreate(MachineBase):
    pass

class MachineUpdate(BaseModel):
    device_number: Optional[str] = None
    sn_code: Optional[str] = None
    device_type: Optional[str] = None
    purchase_date: Optional[date] = None
    warranty_expire_date: Optional[date] = None

class Machine(MachineBase):
    id: int
    user_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

# 租赁相关模式
class RentalBase(BaseModel):
    device_number: Optional[str] = None
    xianyu_order: Optional[str] = None
    send_tracking: Optional[str] = None
    receive_tracking: Optional[str] = None
    send_date: Optional[datetime] = None
    send_status: Optional[str] = None
    customer_name: Optional[str] = None
    address: Optional[str] = None
    start_date: date
    end_date: date
    rent_amount: float
    accessories_note: str
    note: Optional[str] = None

class RentalCreate(RentalBase):
    pass

class RentalUpdate(BaseModel):
    device_number: Optional[str] = None
    xianyu_order: Optional[str] = None
    send_tracking: Optional[str] = None
    receive_tracking: Optional[str] = None
    send_date: Optional[datetime] = None
    send_status: Optional[str] = None
    customer_name: Optional[str] = None
    address: Optional[str] = None
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    rent_amount: Optional[float] = None
    accessories_note: Optional[str] = None
    note: Optional[str] = None

class Rental(RentalBase):
    id: int
    user_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

# 自动设备分配相关模式
class DeviceAllocationConfig(BaseModel):
    prefer_same_device: bool = True    # 优先保持原设备
    consider_location: bool = False    # 是否考虑地理位置
    allow_device_upgrade: bool = True  # 是否允许设备升级
    buffer_days: int = 1               # 设备归还后的维护间隔天数

class DeviceChange(BaseModel):
    rental_id: int
    customer_name: Optional[str] = None
    rental_time_start: date            # 客户租赁开始时间（不变）
    rental_time_end: date              # 客户租赁结束时间（不变）
    original_device: str               # 原设备编号
    new_device: str                    # 新设备编号
    change_reason: str                 # 变更原因
    status: str                        # success/conflict/warning
    conflicts: List[str] = []          # 冲突信息

class DeviceAllocationPreview(BaseModel):
    summary: dict                      # 统计摘要
    device_changes: List[DeviceChange] # 设备分配变更列表

class DeviceAllocationConfirmData(BaseModel):
    device_changes: List[DeviceChange]
    auto_update_send_status: bool = True  # 是否自动更新发货状态

# 保留原有模式以兼容性（标记为废弃）
class AutoScheduleConfig(BaseModel):
    buffer_days: int = 1          # 间隔天数
    priority_rules: List[str] = ["time", "device", "duration"]  # 优先级规则
    include_weekends: bool = True # 是否包含周末
    max_reschedule_days: int = 30 # 最大调整天数

class ScheduleChange(BaseModel):
    rental_id: int
    device_number: str
    customer_name: Optional[str] = None
    original_start_date: date
    original_end_date: date
    new_start_date: date
    new_end_date: date
    status: str                   # success/conflict/warning
    reason: str
    conflicts: List[str] = []     # 冲突信息

class AutoSchedulePreview(BaseModel):
    summary: dict                 # 统计摘要
    schedule_changes: List[ScheduleChange]  # 排单变更列表

class ScheduleConfirmData(BaseModel):
    schedule_changes: List[ScheduleChange]
    auto_update_send_status: bool = True  # 是否自动更新发货状态 