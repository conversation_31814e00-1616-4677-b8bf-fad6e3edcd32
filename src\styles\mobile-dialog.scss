// 移动端弹窗适配样式
// 专门针对iPhone等移动设备优化

// 移动端媒体查询断点
$mobile-breakpoint: 768px;
$small-mobile-breakpoint: 480px;

// 移动端弹窗样式
@media (max-width: $mobile-breakpoint) {
  // 全局按钮优化
  .el-button {
    // 确保按钮在移动端有合适的触摸目标大小
    min-height: 44px;
    
    &.el-button--mini {
      min-height: 36px;
      padding: 8px 12px;
    }
    
    &.el-button--small {
      min-height: 40px;
      padding: 10px 16px;
    }
    
    &.el-button--medium {
      min-height: 44px;
      padding: 12px 20px;
    }
  }
  
  // 表格中的按钮优化
  .el-table {
    .el-button {
      margin: 2px;
      
      &.el-button--mini {
        min-height: 32px;
        padding: 6px 10px;
        font-size: 12px;
      }
    }
  }
  .el-dialog__wrapper {
    padding: 0;
    
    .el-dialog {
      width: 95% !important;
      margin: 10px auto !important;
      max-width: none !important;
      max-height: calc(100vh - 20px) !important;
      overflow-y: auto;
      
      // 针对iPhone的特殊处理
      @supports (-webkit-touch-callout: none) {
        max-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom) - 20px) !important;
      }
    }
    
    .el-dialog__header {
      padding: 15px 20px 10px;
      
      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
      }
      
      .el-dialog__close {
        font-size: 18px;
        top: 15px;
        right: 15px;
      }
    }
    
    .el-dialog__body {
      padding: 10px 20px;
      max-height: calc(100vh - 140px);
      overflow-y: auto;
      
      // 针对iPhone的特殊处理
      @supports (-webkit-touch-callout: none) {
        max-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom) - 140px);
      }
    }
    
    .el-dialog__footer {
      padding: 10px 20px 20px;
      text-align: center;
      
      .el-button {
        min-width: 80px;
        padding: 12px 20px;
        font-size: 14px;
        margin: 0 5px;
        border-radius: 8px;
        
        // 触摸友好的按钮尺寸
        min-height: 44px;
        
        &:first-child {
          margin-left: 0;
        }
        
        &:last-child {
          margin-right: 0;
        }
        
        // 主要按钮样式优化
        &.el-button--primary {
          background: #007aff;
          border-color: #007aff;
          
          &:hover {
            background: #0056b3;
            border-color: #0056b3;
          }
        }
        
        // 取消按钮样式
        &.el-button--default {
          border: 1px solid #dcdfe6;
          
          &:hover {
            background: #f5f7fa;
            border-color: #c0c4cc;
          }
        }
      }
    }
  }
  
  // 表单在移动端的优化
  .el-form {
    .el-form-item {
      margin-bottom: 15px;
      
      .el-form-item__label {
        line-height: 1.5;
        padding-bottom: 5px;
        font-size: 14px;
        font-weight: 500;
      }
      
      .el-form-item__content {
        line-height: 1.5;
        
        .el-input__inner,
        .el-textarea__inner,
        .el-select .el-input__inner,
        .el-date-editor.el-input,
        .el-date-editor.el-input__inner {
          height: 44px;
          font-size: 16px;
          padding: 0 15px;
          border-radius: 8px;
          
          // 防止iPhone上的输入框缩放
          -webkit-text-size-adjust: 100%;
          -webkit-appearance: none;
        }
        
        .el-textarea__inner {
          height: auto;
          min-height: 44px;
          padding: 12px 15px;
          line-height: 1.5;
        }
        
        .el-input-number {
          width: 100%;
          
          .el-input__inner {
            height: 44px;
            font-size: 16px;
            padding: 0 50px 0 15px;
          }
        }
        
        .el-select {
          width: 100%;
          
          .el-input__inner {
            height: 44px;
            font-size: 16px;
            padding: 0 30px 0 15px;
          }
        }
        
        .el-date-editor {
          width: 100%;
          
          .el-input__inner {
            height: 44px;
            font-size: 16px;
            padding: 0 30px 0 15px;
          }
        }
      }
    }
    
    // 移动端表单行布局调整
    .el-row {
      .el-col {
        margin-bottom: 0;
        
        &:not(:last-child) {
          margin-bottom: 15px;
        }
      }
    }
  }
  
  // 移动端表格样式调整
  .detail-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    
    td {
      padding: 8px 12px;
      border: 1px solid #e4e7ed;
      vertical-align: top;
      
      &.label {
        background-color: #f5f7fa;
        font-weight: 500;
        width: 25%;
        min-width: 80px;
      }
      
      &.value {
        width: 25%;
        word-break: break-all;
      }
    }
  }
}

// 小屏幕手机特殊优化
@media (max-width: $small-mobile-breakpoint) {
  .el-dialog__wrapper {
    .el-dialog {
      width: 98% !important;
      margin: 5px auto !important;
      max-height: calc(100vh - 10px) !important;
      
      // 针对iPhone的特殊处理
      @supports (-webkit-touch-callout: none) {
        max-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom) - 10px) !important;
      }
    }
    
    .el-dialog__header {
      padding: 12px 15px 8px;
      
      .el-dialog__title {
        font-size: 15px;
      }
      
      .el-dialog__close {
        font-size: 16px;
        top: 12px;
        right: 12px;
      }
    }
    
    .el-dialog__body {
      padding: 8px 15px;
      max-height: calc(100vh - 120px);
      
      // 针对iPhone的特殊处理
      @supports (-webkit-touch-callout: none) {
        max-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom) - 120px);
      }
    }
    
    .el-dialog__footer {
      padding: 8px 15px 15px;
      
      .el-button {
        min-width: 70px;
        padding: 10px 15px;
        font-size: 13px;
        margin: 0 3px;
        border-radius: 6px;
        
        // 触摸友好的按钮尺寸
        min-height: 40px;
        
        // 主要按钮样式优化
        &.el-button--primary {
          background: #007aff;
          border-color: #007aff;
          
          &:hover {
            background: #0056b3;
            border-color: #0056b3;
          }
        }
        
        // 取消按钮样式
        &.el-button--default {
          border: 1px solid #dcdfe6;
          
          &:hover {
            background: #f5f7fa;
            border-color: #c0c4cc;
          }
        }
      }
    }
  }
  
  // 小屏幕表单优化
  .el-form {
    .el-form-item {
      margin-bottom: 12px;
      
      .el-form-item__label {
        font-size: 13px;
        padding-bottom: 3px;
      }
      
      .el-form-item__content {
        .el-input__inner,
        .el-textarea__inner,
        .el-select .el-input__inner,
        .el-date-editor.el-input,
        .el-date-editor.el-input__inner {
          height: 40px;
          font-size: 15px;
          padding: 0 12px;
        }
        
        .el-textarea__inner {
          min-height: 40px;
          padding: 10px 12px;
        }
        
        .el-input-number .el-input__inner {
          padding: 0 45px 0 12px;
        }
        
        .el-select .el-input__inner,
        .el-date-editor .el-input__inner {
          padding: 0 25px 0 12px;
        }
      }
    }
    
    // 小屏幕下强制单列布局
    .el-row .el-col {
      width: 100% !important;
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  // 小屏幕表格布局调整
  .detail-table {
    font-size: 13px;
    
    td {
      padding: 6px 8px;
      
      &.label {
        width: 30%;
        min-width: 70px;
      }
      
      &.value {
        width: 70%;
      }
    }
    
    // 小屏幕下表格垂直布局
    tr {
      display: block;
      border: 1px solid #e4e7ed;
      margin-bottom: 8px;
      border-radius: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      td {
        display: block;
        width: 100% !important;
        border: none;
        border-bottom: 1px solid #f0f2f5;
        
        &:last-child {
          border-bottom: none;
        }
        
        &.label {
          background-color: #f8f9fa;
          font-weight: 600;
          padding: 8px 12px 4px;
        }
        
        &.value {
          padding: 4px 12px 8px;
        }
      }
    }
  }
}

// 横屏模式优化
@media (max-width: $mobile-breakpoint) and (orientation: landscape) {
  .el-dialog__wrapper {
    .el-dialog {
      max-height: calc(100vh - 10px) !important;
      
      // 针对iPhone的特殊处理
      @supports (-webkit-touch-callout: none) {
        max-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom) - 10px) !important;
      }
    }
    
    .el-dialog__body {
      max-height: calc(100vh - 100px);
      
      // 针对iPhone的特殊处理
      @supports (-webkit-touch-callout: none) {
        max-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom) - 100px);
      }
    }
  }
}

// 解决iOS Safari的滚动问题
@supports (-webkit-touch-callout: none) {
  .el-dialog__wrapper {
    .el-dialog__body {
      -webkit-overflow-scrolling: touch;
    }
  }
}

// 针对iPhone的视口高度修复
@media (max-width: $mobile-breakpoint) {
  // 修复iPhone上的100vh问题
  .el-dialog__wrapper {
    height: 100vh;
    height: -webkit-fill-available;
    
    .el-dialog {
      // 使用更稳定的高度计算
      max-height: 90vh !important;
      max-height: calc(90 * var(--vh, 1vh)) !important;
      
      // 针对iPhone的特殊处理
      @supports (-webkit-touch-callout: none) {
        max-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom) - 40px) !important;
      }
    }
    
    .el-dialog__body {
      max-height: 70vh;
      max-height: calc(70 * var(--vh, 1vh));
      
      // 针对iPhone的特殊处理
      @supports (-webkit-touch-callout: none) {
        max-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom) - 160px);
      }
    }
  }
}

// 针对iPhone X及以上机型的刘海屏适配
@supports (padding: max(0px)) {
  @media (max-width: $mobile-breakpoint) {
    .el-dialog__wrapper {
      .el-dialog {
        max-height: calc(100vh - max(20px, env(safe-area-inset-top)) - max(20px, env(safe-area-inset-bottom))) !important;
      }
      
      .el-dialog__body {
        max-height: calc(100vh - max(140px, env(safe-area-inset-top) + env(safe-area-inset-bottom) + 140px));
      }
    }
  }
  
  @media (max-width: $small-mobile-breakpoint) {
    .el-dialog__wrapper {
      .el-dialog {
        max-height: calc(100vh - max(10px, env(safe-area-inset-top)) - max(10px, env(safe-area-inset-bottom))) !important;
      }
      
      .el-dialog__body {
        max-height: calc(100vh - max(120px, env(safe-area-inset-top) + env(safe-area-inset-bottom) + 120px));
      }
    }
  }
}

// 添加JavaScript来设置自定义vh单位
@media (max-width: $mobile-breakpoint) {
  :root {
    --vh: 1vh;
  }
}

// 基于body类的额外样式
.mobile-viewport {
  .el-dialog__wrapper {
    .el-dialog {
      border-radius: 12px 12px 0 0;
      
      .el-dialog__header {
        border-radius: 12px 12px 0 0;
      }
    }
  }
}

.small-mobile-viewport {
  .el-dialog__wrapper {
    .el-dialog {
      border-radius: 8px 8px 0 0;
      
      .el-dialog__header {
        border-radius: 8px 8px 0 0;
      }
    }
  }
}

.iphone-viewport {
  .el-dialog__wrapper {
    .el-dialog__body {
      // iPhone特有的滚动优化
      -webkit-overflow-scrolling: touch;
      overflow-scrolling: touch;
    }
  }
}

.iphone-x-viewport {
  .el-dialog__wrapper {
    .el-dialog {
      // 为刘海屏iPhone添加额外的顶部间距
      margin-top: env(safe-area-inset-top, 20px);
      margin-bottom: env(safe-area-inset-bottom, 20px);
    }
  }
} 