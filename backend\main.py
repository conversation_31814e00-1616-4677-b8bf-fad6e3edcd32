from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from database.database import engine, Base
from routers import auth, machines, rentals, dashboard, blacklist, scripts
from models import User, Machine, Rental, Script

app = FastAPI(
    title="租赁管理系统API",
    description="设备租赁管理系统后端API",
    version="1.0.0"
)

# 创建数据库表
Base.metadata.create_all(bind=engine)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:9527"],  # Vue前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含路由
app.include_router(auth.router, prefix="/api")
app.include_router(machines.router, prefix="/api")
app.include_router(rentals.router, prefix="/api")
app.include_router(dashboard.router, prefix="/api")
app.include_router(blacklist.router, prefix="/api")
app.include_router(scripts.router, prefix="/api")

@app.get("/")
def read_root():
    return {"message": "租赁管理系统API"}

@app.get("/health")
def health_check():
    return {"status": "healthy"} 