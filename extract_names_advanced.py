#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级姓名提取脚本
从文本文件中提取姓名并保存到CSV文件中，提供详细的统计信息
"""

import re
import csv
import os
import argparse
from collections import Counter

def extract_names_from_file(input_file, output_file, min_length=2, max_length=4, include_stats=True):
    """
    从输入文件中提取姓名并保存到CSV文件
    
    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出CSV文件路径
        min_length (int): 姓名最小长度
        max_length (int): 姓名最大长度
        include_stats (bool): 是否包含统计信息
    """
    names = []
    all_lines = []
    
    try:
        # 读取文件内容
        with open(input_file, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # 按行分割内容
        lines = content.strip().split('\n')
        
        # 定义中文姓名的正则表达式
        chinese_name_pattern = re.compile(f'^[\u4e00-\u9fff]{{{min_length},{max_length}}}$')
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            all_lines.append(line)
            
            if line:
                # 检查是否为纯中文姓名
                if chinese_name_pattern.match(line):
                    names.append(line)
        
        # 去重并排序
        unique_names = sorted(list(set(names)))
        
        # 保存到CSV文件
        with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            # 写入表头
            writer.writerow(['序号', '姓名', '字符数'])
            # 写入数据
            for i, name in enumerate(unique_names, 1):
                writer.writerow([i, name, len(name)])
        
        print(f"✅ 成功提取 {len(unique_names)} 个唯一姓名")
        print(f"📁 结果已保存到: {output_file}")
        
        if include_stats:
            print_statistics(names, unique_names, all_lines)
        
        # 显示前10个姓名作为预览
        print("\n📋 前10个姓名预览:")
        for i, name in enumerate(unique_names[:10], 1):
            print(f"  {i:2d}. {name} ({len(name)}字)")
        
        if len(unique_names) > 10:
            print(f"  ... 还有 {len(unique_names) - 10} 个姓名")
            
        return unique_names
            
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {input_file}")
        return []
    except Exception as e:
        print(f"❌ 处理文件时发生错误: {e}")
        return []

def print_statistics(names, unique_names, all_lines):
    """打印统计信息"""
    print("\n📊 统计信息:")
    print(f"  总行数: {len(all_lines)}")
    print(f"  识别为姓名的行数: {len(names)}")
    print(f"  唯一姓名数量: {len(unique_names)}")
    print(f"  重复姓名数量: {len(names) - len(unique_names)}")
    
    # 姓名长度分布
    length_counter = Counter(len(name) for name in unique_names)
    print(f"\n📏 姓名长度分布:")
    for length in sorted(length_counter.keys()):
        count = length_counter[length]
        percentage = (count / len(unique_names)) * 100
        print(f"  {length}字姓名: {count:3d} 个 ({percentage:5.1f}%)")
    
    # 最常见的姓名
    name_counter = Counter(names)
    most_common = name_counter.most_common(10)
    if len(most_common) > 1 and most_common[0][1] > 1:
        print(f"\n🔄 重复出现的姓名 (前10个):")
        for name, count in most_common:
            if count > 1:
                print(f"  {name}: {count} 次")
    
    # 姓氏分布（取第一个字符）
    surname_counter = Counter(name[0] for name in unique_names)
    most_common_surnames = surname_counter.most_common(10)
    print(f"\n👥 最常见的姓氏 (前10个):")
    for surname, count in most_common_surnames:
        percentage = (count / len(unique_names)) * 100
        print(f"  {surname}: {count:3d} 个 ({percentage:5.1f}%)")

def create_surname_analysis(names, output_file):
    """创建姓氏分析CSV文件"""
    surname_counter = Counter(name[0] for name in names)
    
    surname_file = output_file.replace('.csv', '_surname_analysis.csv')
    with open(surname_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['姓氏', '人数', '百分比'])
        
        total = len(names)
        for surname, count in surname_counter.most_common():
            percentage = (count / total) * 100
            writer.writerow([surname, count, f"{percentage:.2f}%"])
    
    print(f"📊 姓氏分析已保存到: {surname_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='从文本文件中提取姓名')
    parser.add_argument('input_file', nargs='?', 
                       default='backend/PixPin_2025-07-18_01-24-52.txt',
                       help='输入文件路径')
    parser.add_argument('output_file', nargs='?', 
                       default='extracted_names.csv',
                       help='输出CSV文件路径')
    parser.add_argument('--min-length', type=int, default=2,
                       help='姓名最小长度 (默认: 2)')
    parser.add_argument('--max-length', type=int, default=4,
                       help='姓名最大长度 (默认: 4)')
    parser.add_argument('--no-stats', action='store_true',
                       help='不显示统计信息')
    parser.add_argument('--surname-analysis', action='store_true',
                       help='生成姓氏分析文件')
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input_file):
        print(f"❌ 错误: 输入文件 {args.input_file} 不存在")
        return
    
    print(f"🚀 开始处理文件: {args.input_file}")
    print(f"📝 姓名长度范围: {args.min_length}-{args.max_length} 字符")
    
    unique_names = extract_names_from_file(
        args.input_file, 
        args.output_file, 
        args.min_length, 
        args.max_length, 
        not args.no_stats
    )
    
    if args.surname_analysis and unique_names:
        create_surname_analysis(unique_names, args.output_file)
    
    print(f"\n✨ 处理完成！")

if __name__ == "__main__":
    main() 