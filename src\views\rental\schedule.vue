<template>
  <div class="schedule-container">
    <!-- 月份导航栏 -->
    <div class="month-navigation">
      <el-button type="primary" icon="el-icon-arrow-left" @click="prevMonth">上个月</el-button>
      <span class="current-month">{{ currentYear }}年 {{ currentMonth }}月</span>
      <el-button type="primary" icon="el-icon-arrow-right" @click="nextMonth">下个月</el-button>
    </div>

    <!-- 图例说明 -->
    <div class="legend-container">
      <div class="legend-title">图例说明：</div>
      <div class="legend-items">
        <div class="legend-item">
          <div class="legend-color rented-color"></div>
          <span>已租赁</span>
        </div>
        <div class="legend-item">
          <div class="legend-color shipping-color"></div>
          <span>发货间隔期（发货时间到开始时间）</span>
        </div>
        <div class="legend-item">
          <div class="legend-color return-color"></div>
          <span>退回间隔期（结束时间后对应天数）</span>
        </div>
        <div class="legend-item">
          <div class="legend-color weekend-color"></div>
          <span>周末</span>
        </div>
      </div>
    </div>

    <!-- 排单表格 -->
    <div class="schedule-table-container" v-loading="loading">
      <div class="schedule-table">
        <!-- 表头 -->
        <div class="schedule-header">
          <div class="device-header">设备编号</div>
          <div class="date-headers">
            <div class="date-header" v-for="day in daysInMonth" :key="day" :class="{ 'today': isToday(day) }">
              <div class="day-number">{{ day }}</div>
              <div class="weekday">{{ getWeekday(currentYear, currentMonth, day) }}</div>
            </div>
          </div>
        </div>

        <!-- 表格内容 -->
        <div class="schedule-body">
          <div 
            class="schedule-row" 
            v-for="device in scheduleData.devices" 
            :key="device.device_number"
          >
            <div class="device-cell">
              <div class="device-number">{{ device.device_number }}</div>
              <div class="device-sn">{{ maskSnCode(device.sn_code) }}</div>
            </div>
            <div class="date-cells">
              <div 
                class="date-cell"
                v-for="dayInfo in device.rental_days"
                :key="dayInfo.day"
                :class="{
                  'rented': dayInfo.is_rented,
                  'weekend': isWeekend(currentYear, currentMonth, dayInfo.day),
                  'today-cell': isToday(dayInfo.day),
                  'selecting': isInSelection(device.device_number, dayInfo.day),
                  'selected': isSelected(device.device_number, dayInfo.day),
                  'shipping-gap': isShippingGap(device.device_number, dayInfo.day),
                  'return-gap': isReturnGap(device.device_number, dayInfo.day)
                }"
                :data-device="device.device_number"
                :data-day="dayInfo.day"
                @click="showRentalInfo(dayInfo)"
                @mousedown="startSelection($event, device.device_number, dayInfo.day)"
                @mousemove="updateSelection($event, device.device_number, dayInfo.day)"
                @mouseup="endSelection($event, device.device_number, dayInfo.day)"
                @contextmenu="showContextMenu($event, device.device_number, dayInfo.day)"
              >
                <div class="cell-content">
                  <span v-if="dayInfo.is_rented" 
                        class="rental-mark" 
                        :class="getRentalMarkClass(dayInfo)">●</span>
                  <span v-else-if="isShippingDate(device.device_number, dayInfo.day)"
                        class="gap-mark shipping-mark">发</span>
                  <span v-else-if="isReturnGap(device.device_number, dayInfo.day)"
                        class="gap-mark return-mark">退</span>
                </div>
                <!-- 租赁信息提示 -->
                <el-tooltip 
                  v-if="dayInfo.is_rented && dayInfo.rental_info" 
                  effect="dark" 
                  placement="top"
                >
                  <div slot="content">
                    <div>客户：{{ dayInfo.rental_info.customer_name || '未填写' }}</div>
                    <div>租期：{{ dayInfo.rental_info.start_date }} 至 {{ dayInfo.rental_info.end_date }}</div>
                    <div>租金：¥{{ dayInfo.rental_info.rent_amount }}</div>
                    <div>状态：{{ dayInfo.rental_info.send_status || '未发货' }}</div>
                    <div v-if="dayInfo.rental_info.send_date">发货时间：{{ formatDate(dayInfo.rental_info.send_date) }}</div>
                    <div v-if="dayInfo.rental_info.address">地址：{{ dayInfo.rental_info.address }}</div>
                  </div>
                  <div class="rental-tooltip-trigger"></div>
                </el-tooltip>
                
                <!-- 发货日期提示 -->
                <el-tooltip 
                  v-else-if="isShippingDate(device.device_number, dayInfo.day)" 
                  effect="dark" 
                  placement="top"
                >
                  <div slot="content">
                    <div>发货日期</div>
                    <div>设备发货</div>
                  </div>
                  <div class="rental-tooltip-trigger"></div>
                </el-tooltip>
                
                <!-- 发货间隔期提示 -->
                <el-tooltip 
                  v-else-if="isShippingGap(device.device_number, dayInfo.day)" 
                  effect="dark" 
                  placement="top"
                >
                  <div slot="content">
                    <div>发货到开始时间间隔期</div>
                    <div>设备准备寄送中</div>
                  </div>
                  <div class="rental-tooltip-trigger"></div>
                </el-tooltip>
                
                <!-- 退回间隔期提示 -->
                <el-tooltip 
                  v-else-if="isReturnGap(device.device_number, dayInfo.day)" 
                  effect="dark" 
                  placement="top"
                >
                  <div slot="content">
                    <div>设备退回间隔期</div>
                    <div>等待设备退回</div>
                  </div>
                  <div class="rental-tooltip-trigger"></div>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div 
      v-show="contextMenuVisible" 
      :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
      class="context-menu"
      @click="contextMenuVisible = false"
    >
      <div class="context-menu-item" @click="createRentalForSelection">
        <i class="el-icon-plus"></i>
        创建租赁记录
      </div>
    </div>

    <!-- 租赁详情对话框 -->
    <el-dialog title="租赁详情" :visible.sync="rentalDialogVisible" :width="dialogWidth" :class="{'mobile-dialog': isMobile}" @close="handleDetailDialogClose">
      <div style="padding: 20px;">
        <table class="detail-table">
          <tr>
            <td class="label">记录ID：</td>
            <td class="value">{{ selectedRental && selectedRental.rental_info ? selectedRental.rental_info.id || '-' : '-' }}</td>
            <td class="label">设备编号：</td>
            <td class="value">
              <span v-if="!isEditingDevice">{{ selectedRental ? selectedRental.device_number || '-' : '-' }}</span>
              <el-select 
                v-if="isEditingDevice"
                v-model="editDeviceNumber" 
                placeholder="请选择设备编号" 
                filterable 
                clearable
                size="small"
                style="width: 120px"
              >
                <el-option
                  v-for="machine in machineOptions"
                  :key="machine.id"
                  :label="machine.device_number"
                  :value="machine.device_number"
                >
                  <span style="float: left">{{ machine.device_number }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ machine.sn_code }}</span>
                </el-option>
              </el-select>
              <el-button 
                v-if="!isEditingDevice" 
                size="mini" 
                type="text" 
                @click="startEditDevice"
                style="margin-left: 5px;"
              >
                <i class="el-icon-edit"></i>
              </el-button>
              <div v-if="isEditingDevice" style="margin-top: 5px;">
                <el-button 
                  size="mini" 
                  type="primary" 
                  @click="confirmEditDevice"
                  :loading="editingDevice"
                >
                  确认
                </el-button>
                <el-button 
                  size="mini" 
                  @click="cancelEditDevice"
                >
                  取消
                </el-button>
              </div>
            </td>
          </tr>
          <tr>
            <td class="label">客户姓名：</td>
            <td class="value">{{ selectedRental && selectedRental.rental_info ? selectedRental.rental_info.customer_name || '-' : '-' }}</td>
            <td class="label">租金：</td>
            <td class="value">¥{{ selectedRental && selectedRental.rental_info ? selectedRental.rental_info.rent_amount || 0 : 0 }}</td>
          </tr>
          <tr>
            <td class="label">开始时间：</td>
            <td class="value">{{ selectedRental && selectedRental.rental_info ? selectedRental.rental_info.start_date || '-' : '-' }}</td>
            <td class="label">结束时间：</td>
            <td class="value">{{ selectedRental && selectedRental.rental_info ? selectedRental.rental_info.end_date || '-' : '-' }}</td>
          </tr>
          <tr>
            <td class="label">发货状态：</td>
            <td class="value">
              <el-tag :type="selectedRental && selectedRental.rental_info ? statusTagType(selectedRental.rental_info.send_status) : 'info'">{{ selectedRental && selectedRental.rental_info ? selectedRental.rental_info.send_status || '未发货' : '未发货' }}</el-tag>
            </td>
            <td class="label">发货时间：</td>
            <td class="value">{{ selectedRental && selectedRental.rental_info && selectedRental.rental_info.send_date ? formatDateTime(selectedRental.rental_info.send_date) : '-' }}</td>
          </tr>
          <tr>
            <td class="label">闲鱼单号：</td>
            <td class="value">{{ selectedRental && selectedRental.rental_info ? selectedRental.rental_info.xianyu_order || '-' : '-' }}</td>
            <td class="label"></td>
            <td class="value"></td>
          </tr>
          <tr>
            <td class="label">寄出快递单号：</td>
            <td class="value">
              <span>{{ selectedRental && selectedRental.rental_info ? selectedRental.rental_info.send_tracking || '-' : '-' }}</span>
              <el-button 
                v-if="selectedRental && selectedRental.rental_info && selectedRental.rental_info.send_tracking" 
                size="mini" 
                type="primary" 
                @click="queryLogistics(selectedRental.rental_info.send_tracking)"
                style="margin-left: 10px;"
              >
                查询物流
              </el-button>
            </td>
            <td class="label">收货快递单号：</td>
            <td class="value">
              <span>{{ selectedRental && selectedRental.rental_info ? selectedRental.rental_info.receive_tracking || '-' : '-' }}</span>
              <el-button 
                v-if="selectedRental && selectedRental.rental_info && selectedRental.rental_info.receive_tracking" 
                size="mini" 
                type="primary" 
                @click="queryLogistics(selectedRental.rental_info.receive_tracking)"
                style="margin-left: 10px;"
              >
                查询物流
              </el-button>
            </td>
          </tr>
          <tr>
            <td class="label">地址：</td>
            <td class="value" colspan="3">{{ selectedRental && selectedRental.rental_info ? selectedRental.rental_info.address || '-' : '-' }}</td>
          </tr>
          <tr>
            <td class="label">配件备注：</td>
            <td class="value" colspan="3">{{ selectedRental && selectedRental.rental_info ? selectedRental.rental_info.accessories_note || '标准配件' : '标准配件' }}</td>
          </tr>
          <tr>
            <td class="label">备注：</td>
            <td class="value" colspan="3">{{ selectedRental && selectedRental.rental_info ? selectedRental.rental_info.note || '-' : '-' }}</td>
          </tr>
          <tr>
            <td class="label">创建时间：</td>
            <td class="value" colspan="3">{{ selectedRental && selectedRental.rental_info && selectedRental.rental_info.created_at ? formatDateTime(selectedRental.rental_info.created_at) : '-' }}</td>
          </tr>
        </table>
      </div>
    </el-dialog>

    <!-- 创建租赁记录对话框 -->
    <el-dialog title="创建租赁记录" :visible.sync="createRentalDialogVisible" :width="createDialogWidth" :class="{'mobile-dialog': isMobile}" @close="handleCreateDialogClose">
      <el-form :model="newRentalForm" :rules="rentalRules" ref="newRentalForm" :label-width="isMobile ? '80px' : '100px'">
        <el-form-item label="设备编号" prop="device_number">
          <el-input v-model="newRentalForm.device_number" disabled></el-input>
        </el-form-item>
        <el-form-item label="开始日期" prop="start_date">
          <el-input v-model="newRentalForm.start_date" disabled></el-input>
        </el-form-item>
        <el-form-item label="结束日期" prop="end_date">
          <el-input v-model="newRentalForm.end_date" disabled></el-input>
        </el-form-item>
        <el-form-item label="客户姓名" prop="customer_name">
          <el-input v-model="newRentalForm.customer_name" placeholder="请输入客户姓名"></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="customer_phone">
          <el-input v-model="newRentalForm.customer_phone" placeholder="请输入联系电话"></el-input>
        </el-form-item>
        <el-form-item label="收货地址" prop="address">
          <el-input 
            v-model="newRentalForm.address" 
            type="textarea" 
            :rows="2"
            placeholder="请输入收货地址"
          ></el-input>
        </el-form-item>
        <el-form-item label="租金金额" prop="rent_amount">
          <el-input-number 
            v-model="newRentalForm.rent_amount" 
            :min="0" 
            :precision="2"
            placeholder="请输入租金金额"
            style="width: 100%"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="配件说明" prop="accessories_note">
          <el-input 
            v-model="newRentalForm.accessories_note" 
            type="textarea" 
            :rows="3"
            placeholder="请输入配件说明（必填）"
          ></el-input>
        </el-form-item>
        <el-form-item label="闲鱼订单号" prop="xianyu_order">
          <el-input v-model="newRentalForm.xianyu_order" placeholder="请输入闲鱼订单号（可选）"></el-input>
        </el-form-item>
        <el-form-item label="发货状态" prop="send_status">
          <el-select v-model="newRentalForm.send_status" placeholder="请选择发货状态" style="width: 100%">
            <el-option label="未发货" value="未发货"></el-option>
            <el-option label="已发货" value="已发货"></el-option>
            <el-option label="已收货" value="已收货"></el-option>
            <el-option label="已退回" value="已退回"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="note">
          <el-input 
            v-model="newRentalForm.note" 
            type="textarea" 
            :rows="2"
            placeholder="请输入备注信息（可选）"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="createRentalDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitNewRental" :loading="submitting">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getScheduleData, createRental, updateRental } from '@/api/rental'
import { getMachines } from '@/api/machine'
import { maskSnCode } from '@/utils'
import { parseTime } from '@/utils'

export default {
  name: 'RentalSchedule',
  data() {
    return {
      loading: false,
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      scheduleData: {
        devices: []
      },
      windowWidth: window.innerWidth,
      rentalDialogVisible: false,
      selectedRental: null,
      
      // 设备编号编辑相关
      isEditingDevice: false,
      editDeviceNumber: '',
      editingDevice: false,
      machineOptions: [],
      
      // 选择功能相关
      isSelecting: false,
      selectionStart: null,
      selectionEnd: null,
      selectedCells: [],
      
      // 右键菜单相关
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      rightClickedCell: null,
      
      // 创建租赁记录相关
      createRentalDialogVisible: false,
      submitting: false,
      newRentalForm: {
        device_number: '',
        start_date: '',
        end_date: '',
        customer_name: '',
        customer_phone: '',
        address: '',
        rent_amount: 0,
        accessories_note: '',
        xianyu_order: '',
        send_status: '未发货',
        note: ''
      },
      rentalRules: {
        customer_name: [
          { required: true, message: '请输入客户姓名', trigger: 'blur' }
        ],
        customer_phone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' }
        ],
        rent_amount: [
          { required: true, message: '请输入租金金额', trigger: 'blur' }
        ],
        accessories_note: [
          { required: true, message: '请输入配件说明', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    daysInMonth() {
      return new Date(this.currentYear, this.currentMonth, 0).getDate()
    },
    isMobile() {
      return this.windowWidth <= 768
    },
    dialogWidth() {
      if (this.windowWidth <= 480) {
        return '98%'
      } else if (this.windowWidth <= 768) {
        return '95%'
      } else {
        return '50%'
      }
    },
    createDialogWidth() {
      if (this.windowWidth <= 480) {
        return '98%'
      } else if (this.windowWidth <= 768) {
        return '95%'
      } else {
        return '70%'
      }
    }
  },
  created() {
    this.loadScheduleData()
    // 添加全局事件监听器
    document.addEventListener('mouseup', this.globalMouseUp)
    document.addEventListener('click', this.hideContextMenu)
  },
  mounted() {
    this.handleResize()
    this.getMachineOptions()
    window.addEventListener('resize', this.handleResize)
  },
  // 页面被keep-alive激活时执行，确保每次进入页面都获取最新数据
  activated() {
    this.loadScheduleData()
  },
  beforeDestroy() {
    // 移除全局事件监听器
    document.removeEventListener('mouseup', this.globalMouseUp)
    document.removeEventListener('click', this.hideContextMenu)
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // SN码脱敏方法
    maskSnCode,
    
    // 格式化日期时间
    formatDateTime(dateStr) {
      if (!dateStr) return '-'
      return parseTime(new Date(dateStr), '{y}-{m}-{d} {h}:{i}')
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '-'
      return parseTime(new Date(dateStr), '{y}-{m}-{d}')
    },
    
    // 处理创建租赁对话框关闭
    handleCreateDialogClose() {
      // 清除表单数据，确保下次打开时不会有缓存数据
      this.newRentalForm = {
        device_number: '',
        start_date: '',
        end_date: '',
        customer_name: '',
        customer_phone: '',
        address: '',
        rent_amount: 0,
        accessories_note: '',
        xianyu_order: '',
        send_status: '未发货',
        note: ''
      }
      if (this.$refs['newRentalForm']) {
        this.$refs['newRentalForm'].clearValidate()
      }
    },
    
    // 处理详情对话框关闭
    handleDetailDialogClose() {
      // 清除详情数据，确保下次打开时不会有缓存数据
      this.selectedRental = null
      this.isEditingDevice = false
      this.editDeviceNumber = ''
      this.editingDevice = false
    },
    
    // 获取设备选项列表
    async getMachineOptions() {
      try {
        const response = await getMachines()
        console.log('获取设备列表响应:', response)
        // 处理新的API响应格式，包含items和total
        if (response && response.items) {
          this.machineOptions = response.items
        } else if (response && response.data && response.data.items) {
          this.machineOptions = response.data.items
        } else {
          // 兼容旧格式
          this.machineOptions = Array.isArray(response) ? response : (response.data || [])
        }
      } catch (error) {
        console.error('获取设备列表失败:', error)
        this.$message.error('获取设备列表失败')
        this.machineOptions = []
      }
    },
    
    // 开始编辑设备编号
    startEditDevice() {
      this.isEditingDevice = true
      this.editDeviceNumber = this.selectedRental ? this.selectedRental.device_number : ''
    },
    
    // 取消编辑设备编号
    cancelEditDevice() {
      this.isEditingDevice = false
      this.editDeviceNumber = ''
    },
    
    // 确认修改设备编号
    async confirmEditDevice() {
      if (!this.editDeviceNumber) {
        this.$message.error('请选择设备编号')
        return
      }
      
      if (!this.selectedRental || !this.selectedRental.rental_info) {
        this.$message.error('租赁信息不存在')
        return
      }
      
      if (this.editDeviceNumber === this.selectedRental.device_number) {
        this.$message.info('设备编号未发生变化')
        this.cancelEditDevice()
        return
      }
      
      try {
        this.editingDevice = true
        
        // 更新租赁记录的设备编号
        await updateRental(this.selectedRental.rental_info.id, {
          device_number: this.editDeviceNumber
        })
        
        this.$message.success('设备编号修改成功')
        
        // 更新本地数据
        this.selectedRental.device_number = this.editDeviceNumber
        
        // 重新加载排单数据
        this.loadScheduleData()
        
        // 退出编辑模式
        this.cancelEditDevice()
        
      } catch (error) {
        console.error('修改设备编号失败:', error)
        this.$message.error('修改设备编号失败')
      } finally {
        this.editingDevice = false
      }
    },
    
    // 根据发货状态获取标签类型
    statusTagType(status) {
      const statusMap = {
        '未发货': 'info',
        '已发货': 'warning',
        '已收货': 'success',
        '已归还': 'success'
      }
      return statusMap[status] || 'info'
    },
    
    // 根据发货状态获取红点样式类
    getRentalMarkClass(dayInfo) {
      if (!dayInfo.is_rented || !dayInfo.rental_info) return '';
      
      const status = dayInfo.rental_info.send_status;
      
      if (status === '已归还') return 'status-returned';
      if (status === '已收货') return 'status-received';
      if (status === '已发货') return 'status-sent';
      return 'status-unsent'; // 默认为未发货
    },

    // 判断是否是发货到开始时间的间隔期
    isShippingGap(deviceNumber, day) {
      const device = this.scheduleData.devices.find(d => d.device_number === deviceNumber);
      if (!device) return false;

      const currentDateStr = `${this.currentYear}-${String(this.currentMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      
      // 查找该设备的所有租赁记录
      for (const rental of this.getAllRentalsForDevice(device)) {
        if (!rental.send_date) continue;
        
        const sendDateStr = rental.send_date.split('T')[0]; // 取日期部分
        const startDateStr = rental.start_date;
        
        // 只有当发货时间早于开始时间时才考虑间隔期
        if (sendDateStr < startDateStr) {
          // 检查当前日期是否在发货日期和开始日期之间（不包括开始日期）
          if (currentDateStr >= sendDateStr && currentDateStr < startDateStr) {
            return true;
          }
        }
      }
      
      return false;
    },

    // 判断是否是发货日期（只在发货日期显示"发字"）
    isShippingDate(deviceNumber, day) {
      const device = this.scheduleData.devices.find(d => d.device_number === deviceNumber);
      if (!device) return false;

      const currentDateStr = `${this.currentYear}-${String(this.currentMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      
      // 查找该设备的所有租赁记录
      for (const rental of this.getAllRentalsForDevice(device)) {
        if (!rental.send_date) continue;
        
        const sendDateStr = rental.send_date.split('T')[0]; // 取日期部分
        const startDateStr = rental.start_date;
        
        // 只有当发货时间早于开始时间时才考虑
        if (sendDateStr < startDateStr) {
          // 检查当前日期是否是发货日期
          if (currentDateStr === sendDateStr) {
            return true;
          }
        }
      }
      
      return false;
    },

    // 判断是否是结束时间后的对应间隔期
    isReturnGap(deviceNumber, day) {
      const device = this.scheduleData.devices.find(d => d.device_number === deviceNumber);
      if (!device) return false;

      const currentDateStr = `${this.currentYear}-${String(this.currentMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      
      // 查找该设备的所有租赁记录
      for (const rental of this.getAllRentalsForDevice(device)) {
        if (!rental.send_date) continue;
        
        const sendDateStr = rental.send_date.split('T')[0]; // 取日期部分
        const startDateStr = rental.start_date;
        const endDateStr = rental.end_date;
        
        // 只有当发货时间早于开始时间时才考虑退回间隔期
        if (sendDateStr < startDateStr) {
          // 计算发货到开始的天数差
          const sendDate = new Date(sendDateStr);
          const startDate = new Date(startDateStr);
          const shippingDays = Math.floor((startDate - sendDate) / (1000 * 60 * 60 * 24));
          
          if (shippingDays > 0) {
          // 计算结束日期后对应的间隔期开始和结束时间
          const endDate = new Date(endDateStr);
          const returnGapStart = new Date(endDate);
          returnGapStart.setDate(returnGapStart.getDate() + 1); // 结束日期的下一天开始
          
          const returnGapEnd = new Date(endDate);
          returnGapEnd.setDate(returnGapEnd.getDate() + shippingDays); // 加上相同的天数
          
          const returnGapStartStr = returnGapStart.toISOString().split('T')[0];
          const returnGapEndStr = returnGapEnd.toISOString().split('T')[0];
          
            // 检查当前日期是否在结束后的间隔期内
            if (currentDateStr >= returnGapStartStr && currentDateStr <= returnGapEndStr) {
              return true;
            }
          }
        }
      }
      
      return false;
    },

    // 获取设备的所有租赁记录（从rental_days中提取）
    getAllRentalsForDevice(device) {
      const rentals = [];
      const processedRentals = new Set();
      
      for (const dayInfo of device.rental_days) {
        if (dayInfo.is_rented && dayInfo.rental_info && !processedRentals.has(dayInfo.rental_info.id)) {
          rentals.push(dayInfo.rental_info);
          processedRentals.add(dayInfo.rental_info.id);
        }
      }
      
      return rentals;
    },
    
    async loadScheduleData() {
      this.loading = true
      try {
        const response = await getScheduleData(this.currentYear, this.currentMonth)
        console.log('派单数据API响应:', response)
        this.scheduleData = response || { devices: [] }
      } catch (error) {
        this.$message.error('加载排单数据失败')
        console.error('派单数据加载错误:', error)
        this.scheduleData = { devices: [] }
      } finally {
        this.loading = false
      }
    },
    prevMonth() {
      if (this.currentMonth === 1) {
        this.currentMonth = 12
        this.currentYear--
      } else {
        this.currentMonth--
      }
      this.loadScheduleData()
    },
    nextMonth() {
      if (this.currentMonth === 12) {
        this.currentMonth = 1
        this.currentYear++
      } else {
        this.currentMonth++
      }
      this.loadScheduleData()
    },
    getWeekday(year, month, day) {
      const date = new Date(year, month - 1, day)
      const weekdays = ['日', '一', '二', '三', '四', '五', '六']
      return weekdays[date.getDay()]
    },
    isWeekend(year, month, day) {
      const date = new Date(year, month - 1, day)
      return date.getDay() === 0 || date.getDay() === 6
    },
    isToday(day) {
      const today = new Date()
      const todayYear = today.getFullYear()
      const todayMonth = today.getMonth() + 1
      const todayDay = today.getDate()
      
      return this.currentYear === todayYear && this.currentMonth === todayMonth && day === todayDay
    },
    showRentalInfo(dayInfo) {
      if (dayInfo.is_rented && dayInfo.rental_info) {
        this.selectedRental = {
          device_number: this.scheduleData.devices.find(device => 
            device.rental_days.includes(dayInfo)
          )?.device_number,
          rental_info: dayInfo.rental_info
        }
        this.rentalDialogVisible = true
      }
    },
    
    // 选择功能方法
    startSelection(event, deviceNumber, day) {
      // 阻止右键菜单的mousedown事件
      if (event.button === 2) return
      
      event.preventDefault()
      this.isSelecting = true
      this.selectionStart = { device: deviceNumber, day }
      this.selectionEnd = { device: deviceNumber, day }
      this.updateSelectedCells()
    },
    
    updateSelection(event, deviceNumber, day) {
      if (!this.isSelecting) return
      
      // 只允许在同一设备行选择
      if (deviceNumber === this.selectionStart.device) {
        this.selectionEnd = { device: deviceNumber, day }
        this.updateSelectedCells()
      }
    },
    
    endSelection(event, deviceNumber, day) {
      if (!this.isSelecting) return
      
      this.isSelecting = false
      // 选择结束，保持选中状态
    },
    
    globalMouseUp() {
      if (this.isSelecting) {
        this.isSelecting = false
      }
    },
    
    updateSelectedCells() {
      if (!this.selectionStart || !this.selectionEnd) return
      
      const startDay = Math.min(this.selectionStart.day, this.selectionEnd.day)
      const endDay = Math.max(this.selectionStart.day, this.selectionEnd.day)
      
      this.selectedCells = []
      for (let day = startDay; day <= endDay; day++) {
        this.selectedCells.push({
          device: this.selectionStart.device,
          day: day
        })
      }
    },
    
    isInSelection(deviceNumber, day) {
      if (!this.isSelecting || !this.selectionStart || !this.selectionEnd) return false
      
      const startDay = Math.min(this.selectionStart.day, this.selectionEnd.day)
      const endDay = Math.max(this.selectionStart.day, this.selectionEnd.day)
      
      return deviceNumber === this.selectionStart.device && day >= startDay && day <= endDay
    },
    
    isSelected(deviceNumber, day) {
      return this.selectedCells.some(cell => cell.device === deviceNumber && cell.day === day)
    },
    
    // 右键菜单方法
    showContextMenu(event, deviceNumber, day) {
      event.preventDefault()
      
      // 如果右键的位置不在选中区域内，则重新选择该单元格
      if (!this.isSelected(deviceNumber, day)) {
        this.selectedCells = [{ device: deviceNumber, day: day }]
      }
      
      this.rightClickedCell = { device: deviceNumber, day }
      this.contextMenuX = event.clientX
      this.contextMenuY = event.clientY
      this.contextMenuVisible = true
    },
    
    hideContextMenu() {
      this.contextMenuVisible = false
    },
    
    // 创建租赁记录方法
    createRentalForSelection() {
      if (this.selectedCells.length === 0) {
        this.$message.warning('请先选择时间段')
        return
      }
      
      // 检查选中的单元格是否有已租赁的
      const hasRentedCell = this.selectedCells.some(cell => {
        const device = this.scheduleData.devices.find(d => d.device_number === cell.device)
        if (device) {
          const dayInfo = device.rental_days.find(d => d.day === cell.day)
          return dayInfo && dayInfo.is_rented
        }
        return false
      })
      
      if (hasRentedCell) {
        this.$message.warning('选中的时间段包含已租赁的日期，无法创建租赁记录')
        return
      }
      
      // 准备表单数据
      const sortedCells = this.selectedCells.sort((a, b) => a.day - b.day)
      const startDay = sortedCells[0].day
      const endDay = sortedCells[sortedCells.length - 1].day
      
      // 修改日期格式为 YYYY-MM-DDT00:00:00
      const startDate = `${this.currentYear}-${String(this.currentMonth).padStart(2, '0')}-${String(startDay).padStart(2, '0')}T00:00:00`
      const endDate = `${this.currentYear}-${String(this.currentMonth).padStart(2, '0')}-${String(endDay).padStart(2, '0')}T00:00:00`
      
      this.newRentalForm = {
        device_number: sortedCells[0].device,
        start_date: startDate,
        end_date: endDate,
        customer_name: '',
        customer_phone: '',
        address: '',
        rent_amount: 0,
        accessories_note: '',
        xianyu_order: '',
        send_status: '未发货',
        note: ''
      }
      
      this.createRentalDialogVisible = true
      this.contextMenuVisible = false
    },
    
    async submitNewRental() {
      this.$refs.newRentalForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true
          try {
            await createRental(this.newRentalForm)
            this.$message.success('租赁记录创建成功')
            this.createRentalDialogVisible = false
            this.selectedCells = [] // 清除选择
            this.loadScheduleData() // 重新加载数据
          } catch (error) {
            this.$message.error('创建租赁记录失败')
            console.error(error)
          } finally {
            this.submitting = false
          }
        }
      })
    },
    
    // 查询物流信息
    queryLogistics(trackingNumber) {
      if (!trackingNumber) return
      
      // 打开快递100查询页面
      const url = `https://www.kuaidi100.com/chaxun?nu=${trackingNumber}`
      window.open(url, '_blank')
    },
    handleResize() {
      this.windowWidth = window.innerWidth
    }
  }
}
</script>

<style lang="scss" scoped>
.schedule-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

// 添加详情表格样式
.detail-table {
  width: 100%;
  border-collapse: collapse;
  
  td {
    padding: 8px 12px;
    border: 1px solid #EBEEF5;
  }
  
  .label {
    width: 120px;
    background-color: #F5F7FA;
    font-weight: bold;
    text-align: right;
  }
  
  .value {
    min-width: 150px;
  }
}

.month-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  gap: 20px;
  
  .current-month {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    min-width: 120px;
    text-align: center;
  }
}

.legend-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
  padding: 15px;
  margin-bottom: 20px;
  
  .legend-title {
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }
  
  .legend-items {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    
    .legend-item {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .legend-color {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        border: 1px solid #ddd;
        
        &.rented-color {
          background: #ffebee;
        }
        
        &.shipping-color {
          background: #fff3cd;
          border-color: #ffc107;
        }
        
        &.return-color {
          background: #fff3cd;
          border-color: #ffc107;
        }
        
        &.weekend-color {
          background: #fafafa;
        }
      }
      
      span {
        font-size: 14px;
        color: #666;
      }
    }
  }
}

.schedule-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.schedule-table {
  width: 100%;
  overflow-x: auto;
}

.schedule-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 2px solid #e9ecef;
  
  .device-header {
    width: 120px;
    min-width: 120px;
    padding: 15px 10px;
    font-weight: bold;
    color: #333;
    border-right: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .date-headers {
    display: flex;
    flex: 1;
    
    .date-header {
      flex: 1;
      min-width: 40px;
      max-width: 60px;
      padding: 10px 5px;
      text-align: center;
      border-right: 1px solid #e9ecef;
      
      .day-number {
        font-weight: bold;
        font-size: 14px;
        color: #333;
      }
      
      .weekday {
        font-size: 12px;
        color: #666;
        margin-top: 2px;
      }
      
      &.today {
        background: #409eff;
        color: white;
        font-weight: bold;
        
        .day-number {
          color: white;
          font-weight: bold;
          font-size: 16px;
        }
        
        .weekday {
          color: #e6f2ff;
        }
      }
    }
  }
}

.schedule-body {
  .schedule-row {
    display: flex;
    border-bottom: 1px solid #e9ecef;
    
    &:hover {
      background: #f8f9fa;
    }
    
    .device-cell {
      width: 120px;
      min-width: 120px;
      padding: 15px 10px;
      border-right: 1px solid #e9ecef;
      
      .device-number {
        font-weight: bold;
        color: #333;
        font-size: 14px;
      }
      
      .device-sn {
        color: #666;
        font-size: 12px;
        margin-top: 4px;
      }
    }
    
    .date-cells {
      display: flex;
      flex: 1;
      
      .date-cell {
        flex: 1;
        min-width: 40px;
        max-width: 60px;
        height: 60px;
        border-right: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          background: #e3f2fd;
        }
        
        &.rented {
          background: #ffebee;
          
          .rental-mark {
            font-size: 16px;
            font-weight: bold;
            
            // 根据发货状态显示不同颜色
            &.status-returned {
              color: #9e9e9e; // 灰色 = 已归还
            }
            
            &.status-received {
              color: #4caf50; // 绿色 = 已收货
            }
            
            &.status-sent {
              color: #4caf50; // 绿色 = 已发货
            }
            
            &.status-unsent {
              color: #f44336; // 红色 = 未发货
            }
          }
          
          &:hover {
            background: #ffcdd2;
          }
        }
        
        &.weekend {
          background: #fafafa;
          
          &.rented {
            background: #ffebee;
          }
        }
        
        &.today-cell {
          border: 2px solid #409eff;
          box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
          
          &.rented {
            background: #e6f2ff;
            border: 2px solid #409eff;
          }
          
          &.weekend {
            background: #f0f8ff;
            border: 2px solid #409eff;
          }
        }
        
        &.selecting {
          background: #e3f2fd !important;
          border: 2px solid #2196f3;
          box-shadow: 0 0 5px rgba(33, 150, 243, 0.3);
        }
        
        &.selected {
          background: #e3f2fd !important;
          border: 2px solid #2196f3;
          box-shadow: 0 0 5px rgba(33, 150, 243, 0.3);
        }
        
        &.shipping-gap {
          background: #fff3cd !important; // 黄色背景 - 发货到开始时间间隔
          border: 1px solid #ffc107;
          
          &:hover {
            background: #ffeaa7 !important;
          }
        }
        
        &.return-gap {
          background: #fff3cd !important; // 黄色背景 - 结束后对应间隔
          border: 1px solid #ffc107;
          
          &:hover {
            background: #ffeaa7 !important;
          }
        }
        
        .cell-content {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .gap-mark {
          font-size: 12px;
          font-weight: bold;
          color: #e67e22;
          
          &.shipping-mark {
            color: #f39c12; // 橙色 - 发货
          }
          
          &.return-mark {
            color: #d35400; // 深橙色 - 退回
          }
        }
        
        .rental-tooltip-trigger {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .schedule-container {
    padding: 10px;
  }
  
  .month-navigation {
    flex-direction: column;
    gap: 10px;
    
    .current-month {
      order: -1;
      margin-bottom: 10px;
    }
  }
  
  .legend-container {
    padding: 10px;
    
    .legend-items {
      gap: 10px;
      
      .legend-item {
        span {
          font-size: 12px;
        }
        
        .legend-color {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
  
  .schedule-header {
    .device-header {
      width: 100px;
      min-width: 100px;
      padding: 10px 5px;
      font-size: 12px;
    }
    
    .date-headers .date-header {
      min-width: 30px;
      max-width: 40px;
      padding: 8px 2px;
      
      .day-number {
        font-size: 12px;
      }
      
      .weekday {
        font-size: 10px;
      }
    }
  }
  
  .schedule-body .schedule-row {
    .device-cell {
      width: 100px;
      min-width: 100px;
      padding: 10px 5px;
      
      .device-number {
        font-size: 12px;
      }
      
      .device-sn {
        font-size: 10px;
      }
    }
    
    .date-cells .date-cell {
      min-width: 30px;
      max-width: 40px;
      height: 45px;
      
      .rental-mark {
        font-size: 12px !important;
      }
    }
  }
}

// 右键菜单样式
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 2000;
  min-width: 150px;
  
  .context-menu-item {
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #606266;
    font-size: 14px;
    
    &:hover {
      background: #f5f7fa;
      color: #409eff;
    }
    
    i {
      font-size: 16px;
    }
  }
}

// 禁用文本选择，避免拖拽时选中文字
.schedule-table {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
</style> 