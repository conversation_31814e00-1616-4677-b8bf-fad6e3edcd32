# Nginx 反向代理部署说明

## 项目概述

这是一个租赁管理系统，包含：
- **前端**: Vue.js + Element UI (端口: 9527)
- **后端**: FastAPI (端口: 8000)
- **数据库**: SQLite

## 部署步骤

### 1. 环境准备

确保系统已安装：
- Node.js (>= 8.9)
- Python 3.7+
- nginx
- pip

### 2. 安装依赖

```bash
# 安装前端依赖
npm install

# 安装后端依赖
cd backend
pip install -r requirements.txt
cd ..
```

### 3. 构建前端

```bash
npm run build:prod
```

### 4. 配置nginx

将 `nginx.conf` 文件复制到nginx配置目录：

```bash
sudo cp nginx.conf /etc/nginx/nginx.conf
```

### 5. 部署静态文件

```bash
# 创建部署目录
sudo mkdir -p /var/www/dazuizulin

# 复制构建文件
sudo cp -r dist/* /var/www/dazuizulin/

# 设置权限
sudo chown -R www-data:www-data /var/www/dazuizulin
sudo chmod -R 755 /var/www/dazuizulin
```

### 6. 启动服务

#### 方法一：使用部署脚本（推荐）

```bash
# 给脚本执行权限
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

#### 方法二：手动启动

```bash
# 启动后端服务
cd backend
nohup python start.py > ../backend.log 2>&1 &

# 测试nginx配置
sudo nginx -t

# 重启nginx
sudo systemctl restart nginx
```

### 7. 验证部署

访问以下地址验证部署是否成功：

- **前端**: http://localhost
- **API**: http://localhost/api
- **健康检查**: http://localhost/health

## 配置说明

### nginx配置特点

1. **反向代理**: 将 `/api/*` 请求代理到后端服务
2. **SPA支持**: 支持Vue.js前端路由
3. **静态资源缓存**: 优化静态资源加载
4. **安全头**: 添加安全相关的HTTP头
5. **错误处理**: 自定义错误页面

### 重要配置项

- **前端路径**: `/var/www/dazuizulin/dist` (请根据实际情况修改)
- **后端地址**: `http://localhost:8000`
- **域名**: `localhost` (请根据实际情况修改)

## 停止服务

使用停止脚本：

```bash
chmod +x stop.sh
./stop.sh
```

或手动停止：

```bash
# 查找并停止后端进程
ps aux | grep "python start.py"
kill <PID>

# 停止nginx
sudo systemctl stop nginx
```

## 日志查看

- **nginx访问日志**: `/var/log/nginx/access.log`
- **nginx错误日志**: `/var/log/nginx/error.log`
- **后端日志**: `backend.log`

## 故障排除

### 1. nginx配置测试失败

```bash
sudo nginx -t
```

检查配置文件语法错误。

### 2. 权限问题

```bash
sudo chown -R www-data:www-data /var/www/dazuizulin
sudo chmod -R 755 /var/www/dazuizulin
```

### 3. 端口占用

检查端口是否被占用：

```bash
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :8000
```

### 4. 后端服务无法启动

检查Python环境和依赖：

```bash
cd backend
python -c "import fastapi"
```

## HTTPS配置（可选）

如需启用HTTPS，请：

1. 获取SSL证书
2. 修改nginx.conf中的HTTPS配置
3. 取消注释HTTPS server块
4. 更新证书路径和域名

## 性能优化

1. **启用gzip压缩**（已在配置中启用）
2. **静态资源缓存**（已在配置中设置）
3. **连接池优化**（已在配置中设置）

## 安全建议

1. 定期更新nginx和系统
2. 配置防火墙规则
3. 使用HTTPS（生产环境）
4. 定期备份数据
5. 监控系统资源使用情况 