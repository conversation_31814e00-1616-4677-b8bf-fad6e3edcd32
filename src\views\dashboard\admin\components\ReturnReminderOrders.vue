<template>
  <el-card class="order-list-card">
    <div slot="header" class="clearfix">
      <span class="card-title">催退回订单</span>
      <div class="header-actions">
        <el-badge :value="orders.length" class="order-count" type="warning" />
        <el-button 
          type="text" 
          size="mini" 
          icon="el-icon-refresh" 
          @click="fetchOrders"
          :loading="loading"
          style="margin-left: 10px;"
        >
          刷新
        </el-button>
      </div>
    </div>
    
    <div v-if="loading" class="loading-container">
      <i class="el-icon-loading"></i>
      <p>加载中...</p>
    </div>
    
    <div v-else-if="orders.length === 0" class="empty-container">
      <i class="el-icon-circle-check"></i>
      <p>暂无需要催退回的订单</p>
    </div>
    
    <div v-else class="order-list">
      <div 
        v-for="order in orders" 
        :key="order.id" 
        class="order-item urgent"
        @click="viewOrderDetail(order)"
      >
        <div class="order-info">
          <div class="order-header">
            <span class="order-number">{{ order.xianyu_order || `订单#${order.id}` }}</span>
            <div class="status-tags">
              <el-tag type="danger" size="mini">明天到期</el-tag>
              <el-tag 
                :type="getStatusType(order.send_status)" 
                size="mini"
              >
                {{ order.send_status || '未发货' }}
              </el-tag>
            </div>
          </div>
          <div class="order-details">
            <p><strong>客户：</strong>{{ order.customer_name || '未填写' }}</p>
            <p><strong>设备：</strong>{{ order.device_number || '未分配' }}</p>
            <p><strong>租赁结束：</strong>{{ formatDate(order.end_date) }}</p>
            <p><strong>收货快递：</strong>{{ order.receive_tracking || '未填写' }}</p>
            <p><strong>租金：</strong>¥{{ order.rent_amount || 0 }}</p>
          </div>
        </div>
        <div class="order-actions">
          <el-button 
            type="warning" 
            size="mini"
            @click.stop="handleRemind(order)"
          >
            联系客户
          </el-button>
          <el-button 
            type="success" 
            size="mini"
            @click.stop="handleReturn(order)"
            v-if="order.send_status !== '已归还'"
          >
            确认归还
          </el-button>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
import { getReturnReminderOrders } from '@/api/dashboard'

export default {
  name: 'ReturnReminderOrders',
  data() {
    return {
      orders: [],
      loading: false
    }
  },
  mounted() {
    this.fetchOrders()
  },
  methods: {
    async fetchOrders() {
      this.loading = true
      try {
        const response = await getReturnReminderOrders()
        this.orders = response || []
      } catch (error) {
        console.error('获取催退回订单失败:', error)
        this.$message.error('获取催退回订单列表失败')
        this.orders = []
      } finally {
        this.loading = false
      }
    },
    getStatusType(status) {
      const statusMap = {
        '已发货': 'success',
        '已收货': 'info',
        '已归还': 'success',
        '未发货': 'warning',
        '待发货': 'warning'
      }
      return statusMap[status] || 'info'
    },
    formatDate(dateStr) {
      if (!dateStr) return '未知'
      const date = new Date(dateStr)
      return date.toLocaleDateString('zh-CN')
    },
    viewOrderDetail(order) {
      this.$router.push({
        path: '/rental/list',
        query: { search: order.xianyu_order || order.id }
      })
    },
    handleRemind(order) {
      const message = `提醒：订单 ${order.xianyu_order || order.id} 将于明天到期，请联系客户 ${order.customer_name || '客户'} 安排设备归还。`
      this.$message({
        message: message,
        type: 'warning',
        duration: 5000
      })
    },
    handleReturn(order) {
      this.$message.info('归还确认功能待集成，请到租赁管理页面操作')
      this.viewOrderDetail(order)
    }
  }
}
</script>

<style lang="scss" scoped>
.order-list-card {
  margin-bottom: 20px;
  
  .card-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }
  
  .header-actions {
    float: right;
    display: flex;
    align-items: center;
  }
  
  .order-count {
    margin-top: 2px;
  }
}

.loading-container,
.empty-container {
  text-align: center;
  padding: 40px 0;
  color: #909399;
  
  i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
    color: #67c23a;
  }
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

.order-list {
  max-height: 400px;
  overflow-y: auto;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
  
  &.urgent {
    border-color: #f56c6c;
    background-color: #fef0f0;
  }
  
  &:hover {
    border-color: #f56c6c;
    box-shadow: 0 2px 8px rgba(245, 108, 108, 0.2);
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.order-info {
  flex: 1;
  
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    .order-number {
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }
    
    .status-tags {
      display: flex;
      gap: 5px;
    }
  }
  
  .order-details {
    p {
      margin: 5px 0;
      font-size: 13px;
      color: #606266;
      
      strong {
        color: #303133;
        margin-right: 5px;
      }
    }
  }
}

.order-actions {
  margin-left: 15px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  justify-content: center;
}

@media (max-width: 768px) {
  .order-item {
    flex-direction: column;
    
    .order-actions {
      margin-left: 0;
      margin-top: 10px;
      flex-direction: row;
      gap: 10px;
    }
  }
  
  .order-header {
    flex-direction: column;
    align-items: flex-start !important;
    
    .status-tags {
      margin-top: 8px;
    }
  }
}
</style>