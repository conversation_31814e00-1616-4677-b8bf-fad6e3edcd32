<template>
  <div class="express-delivery-container">
    <div class="app-container">
      <div class="express-delivery-header">
        <h3>快递时效</h3>
        <p>查看快递运输时效信息</p>
      </div>
      <div class="iframe-container">
        <iframe
          src="https://www.sf-express.com/chn/sc/price-query"
          frameborder="0"
          class="express-iframe"
          title="快递时效查询"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExpressDelivery',
  data() {
    return {}
  },
  mounted() {
    console.log('快递时效页面已加载')
  }
}
</script>

<style lang="scss" scoped>
.express-delivery-container {
  padding: 0;
  height: 100vh;
  
  .app-container {
    height: 100%;
    padding: 20px;
    
    .express-delivery-header {
      margin-bottom: 20px;
      
      h3 {
        margin: 0 0 8px 0;
        color: #303133;
        font-size: 18px;
        font-weight: 500;
      }
      
      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
      }
    }
    
    .iframe-container {
      height: calc(100vh - 140px);
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      overflow: hidden;
      
      .express-iframe {
        width: 100%;
        height: 100%;
        border: none;
      }
    }
  }
}
</style> 